
// Global visualization variables
let svg, visualization, simulationData, simulation;
let svgGroup; // Group element for transformations
let currentView = "llevel";
let statusFilter = "all";
let tooltip;
let currentTransform = { k: 1, x: 0, y: 0, rotation: 0 }; // Track current transformation
let transformHistory = []; // Navigation history
let historyIndex = -1; // Current position in history
let zoomBehavior; // D3 zoom behavior

// When DOM is loaded, initialize visualization
document.addEventListener('DOMContentLoaded', function() {
    // Create tooltip once at initialization
    tooltip = d3.select("body").append("div")
        .attr("class", "visualization-tooltip")
        .style("visibility", "hidden");
        
    // initVisualization() call moved to loadClusterVisualization to ensure container exists
    
    // Setup view button event listeners
    document.querySelectorAll('.view-button').forEach(button => {
        button.addEventListener('click', function() {
            document.querySelectorAll('.view-button').forEach(b => b.classList.remove('active'));
            this.classList.add('active');
            currentView = this.getAttribute('data-view');
            updateVisualizationView();
        });
    });
    
    // Setup filter event listeners
    document.getElementById('statusFilter').addEventListener('change', function() {
        statusFilter = this.value;
        filterVisualization();
    });
// Floating panel event bindings:
document.addEventListener("DOMContentLoaded", ()=> {
    const panel = document.querySelector(".viz-floating-toolbar");
    if(panel){
        const btns = panel.querySelectorAll("button");
        btns.forEach(btn => {
            const title = btn.getAttribute("title").toLowerCase();
            btn.addEventListener("click", ()=>{
                // Match button action
                if(title.includes("zoom in")){
                    svg.transition().duration(300).call(zoomBehavior.scaleBy, 1.3);
                } else if (title.includes("zoom out")){
                    svg.transition().duration(300).call(zoomBehavior.scaleBy, 0.7);
                } else if (title.includes("pan up")){
                    panDirection(0, 50);
                } else if (title.includes("pan down")){
                    panDirection(0, -50);
                } else if (title.includes("pan left")){
                    panDirection(50, 0);
                } else if (title.includes("pan right")){
                    panDirection(-50, 0);
                } else if (title.includes("rotate left")){
                    rotateVisualization(-15);
                } else if (title.includes("rotate right")){
                    rotateVisualization(15);
                } else if (title.includes("reset")){
                    resetView();
                }
            });
        });
    }
});
});

// Main function to initialize the visualization
function initVisualization() {
    // Get container dimensions
    const container = document.getElementById('clusterVisualizationContainer');
    const width = container.clientWidth;
    const height = container.clientHeight;
    
    // Clear any previous visualization
    d3.select(container).select("svg").remove();
    
    // Create SVG element with zoom support
    svg = d3.select(container)
        .append("svg")
        .attr("width", width)
        .attr("height", height)
        .attr("viewBox", [0, 0, width, height])
        .attr("class", "visualization-svg")
        .style("cursor", "grab"); // Indicate draggable area
    
    // Create a group for transformations
    svgGroup = svg.append("g")
        .attr("class", "transform-group");
    
    // Define zoom behavior
    zoomBehavior = d3.zoom()
        .scaleExtent([0.1, 10])  // Expanded min/max zoom scale for greater sensitivity
        .on("zoom", zoomed)
        .filter(event => {
            // Enable zooming even when mouse is over nodes
            // (except when right mouse button is pressed)
            return !event.ctrlKey && !event.button;
        });
    
    // Apply zoom behavior to SVG and enable immediate use
    svg.call(zoomBehavior)
       .on("dblclick.zoom", null) // Disable double-click zoom to prevent conflicts
       .on("mousedown", function() { d3.select(this).style("cursor", "grabbing"); })
       .on("mouseup", function() { d3.select(this).style("cursor", "grab"); });
    
    // Initialize navigation controls
    createNavigationControls(container, width, height);
    
    // Set up keyboard navigation
    setupKeyboardNavigation();
    
    // Add breadcrumb navigation
    createBreadcrumbNavigation(container);
    
    // Load data from API
    loadVisualizationData();
    
    // Add responsive resize handling
    window.addEventListener('resize', debounce(resizeVisualization, 250));
}

// Create breadcrumb navigation
function createBreadcrumbNavigation(container) {
    // Remove existing breadcrumbs if any
    d3.select(".breadcrumb-navigation").remove();
    
    // Create container for breadcrumbs
    const breadcrumbContainer = d3.select(container.parentNode)
        .insert("div", ":first-child")
        .attr("class", "breadcrumb-navigation")
        .style("padding", "10px")
        .style("background-color", "#f8f9fa")
        .style("border-bottom", "1px solid #e9ecef")
        .style("margin-bottom", "15px")
        .style("display", "flex")
        .style("align-items", "center")
        .style("font-size", "14px");
    
    // Add breadcrumb items
    breadcrumbContainer.append("a")
        .attr("href", "#")
        .text("Dashboard")
        .style("color", "#0076CE")
        .style("text-decoration", "none")
        .on("click", function() {
            // Navigate to dashboard
            window.history.pushState({}, "", "/dashboard");
            document.querySelector('.tab-button[data-tab="summary"]')?.click();
        });
    
    breadcrumbContainer.append("span")
        .text(" > ")
        .style("margin", "0 8px")
        .style("color", "#6c757d");
    
    breadcrumbContainer.append("a")
        .attr("href", "#enhanced-correlation")
        .text("Product Correlation")
        .style("color", "#0076CE")
        .style("text-decoration", "none")
        .on("click", function() {
            // Navigate to this view
            window.history.pushState({view: "correlation"}, "", "#enhanced-correlation");
            document.querySelector('.tab-button[data-tab="enhanced-correlation"]')?.click();
        });
}

// Handle window resizing
function resizeVisualization() {
    const container = document.getElementById('clusterVisualizationContainer');
    if (!container || !svg) return;
    
    const width = container.clientWidth;
    const height = container.clientHeight;
    
    // Update SVG dimensions
    svg.attr("width", width)
       .attr("height", height);
    
    // Update forces to re-center visualization
    if (simulation) {
        simulation.force("center", d3.forceCenter(width / 2, height / 2));
        simulation.alpha(0.3).restart();
    }
    
    // Update minimap
    updateMinimap();
}

// Debounce function to prevent excessive resize operations
function debounce(func, wait) {
    let timeout;
    return function() {
        const context = this, args = arguments;
        clearTimeout(timeout);
        timeout = setTimeout(() => func.apply(context, args), wait);
    };
}

// Function to handle zoom/pan events
function zoomed(event) {
    console.log('[DEBUG] zoomed event triggered:', event);
    // Update current transform
    currentTransform.k = event.transform.k;
    currentTransform.x = event.transform.x;
    currentTransform.y = event.transform.y;
    
    // Apply transform to the group with smooth transition if not from direct interaction
    if (event.sourceEvent) {
        // Direct interaction (mouse/touch) - immediate update
        svgGroup.attr("transform", `translate(${event.transform.x},${event.transform.y}) scale(${event.transform.k}) rotate(${currentTransform.rotation})`);
    } else {
        // Programmatic zoom - smooth transition
        svgGroup.transition()
            .duration(300)
            .attr("transform", `translate(${event.transform.x},${event.transform.y}) scale(${event.transform.k}) rotate(${currentTransform.rotation})`);
    }
    
    // Update viewport indicator in minimap
    updateViewportIndicator();
    
    // Add to history if it's a user-initiated event (not from history navigation)
    if (event.sourceEvent && !isNavigatingHistory) {
        addToHistory();
    }
}

// Set up keyboard navigation
function setupKeyboardNavigation() {
    // Add keyboard event listener to the document
    d3.select('body').on('keydown', function(event) {
        // Only process if we're not in an input field
        if (event.target.tagName !== 'INPUT' && event.target.tagName !== 'TEXTAREA') {
            const step = event.shiftKey ? 50 : 20; // Larger steps with shift
            switch (event.key) {
                case 'ArrowUp':
                    panDirection(0, step);
                    event.preventDefault();
                    break;
                case 'ArrowDown':
                    panDirection(0, -step);
                    event.preventDefault();
                    break;
                case 'ArrowLeft':
                    panDirection(step, 0);
                    event.preventDefault();
                    break;
                case 'ArrowRight':
                    panDirection(-step, 0);
                    event.preventDefault();
                    break;
                case '+':
                case '=':
                    svg.transition().duration(300).call(zoomBehavior.scaleBy, 1.5);
                    event.preventDefault();
                    break;
                case '-':
                    svg.transition().duration(300).call(zoomBehavior.scaleBy, 0.65);
                    event.preventDefault();
                    break;
                case 'r':
                    // Reset view
                    resetView();
                    event.preventDefault();
                    break;
                case 'z':
                    if (event.ctrlKey && !event.shiftKey) {
                        // Ctrl+Z: Undo
                        undoNavigation();
                        event.preventDefault();
                    } else if (event.ctrlKey && event.shiftKey) {
                        // Ctrl+Shift+Z: Redo
                        redoNavigation();
                        event.preventDefault();
                    }
                    break;
            }
        }
    });
}

// Pan by relative amount
function panDirection(dx, dy) {
    console.log('[DEBUG] panDirection called, dx:', dx, ', dy:', dy);
    isNavigatingHistory = false;
    
    // Calculate adjusted dx, dy based on current zoom level
    const adjustedDx = dx;  // Do not dampen pan movement relative to zoom factor
    const adjustedDy = dy;
    
    // Update transform
    currentTransform.x += adjustedDx;
    currentTransform.y += adjustedDy;
    
    // Apply transform with transition
    svgGroup.transition()
        .duration(100)
        .attr("transform", `translate(${currentTransform.x},${currentTransform.y}) scale(${currentTransform.k}) rotate(${currentTransform.rotation})`);
    
    // Update viewport indicator
    updateViewportIndicator();
    
    // Add to history
    addToHistory();
}

// Create navigation controls
function createNavigationControls(container, width, height) {
    // Remove existing controls if any
    d3.select(".navigation-controls").remove();
    
    // Create container for controls
    const controlsContainer = d3.select(container)
        .append("div")
        .attr("class", "navigation-controls")
        .style("position", "absolute")
        .style("top", "10px")
        .style("right", "10px")
        .style("background-color", "rgba(255,255,255,0.8)")
        .style("border", "1px solid #ddd")
        .style("border-radius", "4px")
        .style("padding", "5px")
        .style("display", "flex")
        .style("flex-direction", "column")
        .style("gap", "5px")
        .style("z-index", "100");
    
    // Zoom controls
    const zoomControls = controlsContainer.append("div")
        .style("display", "flex")
        .style("gap", "5px");
    
    // Zoom in button
    zoomControls.append("button")
        .attr("class", "nav-button")
        .html("➕")
        .style("width", "30px")
        .style("height", "30px")
        .style("cursor", "pointer")
        .on("click", function() {
            isNavigatingHistory = false;
            svg.transition().duration(300).call(zoomBehavior.scaleBy, 1.3);
        });
    
    // Zoom out button
    zoomControls.append("button")
        .attr("class", "nav-button")
        .html("➖")
        .style("width", "30px")
        .style("height", "30px")
        .style("cursor", "pointer")
        .on("click", function() {
            isNavigatingHistory = false;
            svg.transition().duration(300).call(zoomBehavior.scaleBy, 0.7);
        });
    
    // Rotation controls
    const rotationControls = controlsContainer.append("div")
        .style("display", "flex")
        .style("gap", "5px");
    
    // Rotate left button
    rotationControls.append("button")
        .attr("class", "nav-button")
        .html("↺")
        .style("width", "30px")
        .style("height", "30px")
        .style("cursor", "pointer")
        .on("click", function() {
            rotateVisualization(-15); // Rotate -15 degrees
        });
    
    // Rotate right button
    rotationControls.append("button")
        .attr("class", "nav-button")
        .html("↻")
        .style("width", "30px")
        .style("height", "30px")
        .style("cursor", "pointer")
        .on("click", function() {
            rotateVisualization(15); // Rotate 15 degrees
        });
    
    // History controls
    const historyControls = controlsContainer.append("div")
        .style("display", "flex")
        .style("gap", "5px");
    
    // Undo button
    historyControls.append("button")
        .attr("class", "nav-button")
        .html("↩")
        .style("width", "30px")
        .style("height", "30px")
        .style("cursor", "pointer")
        .attr("disabled", true)
        .on("click", function() {
            undoNavigation();
        });
    
    // Redo button
    historyControls.append("button")
        .attr("class", "nav-button")
        .html("↪")
        .style("width", "30px")
        .style("height", "30px")
        .style("cursor", "pointer")
        .attr("disabled", true)
        .on("click", function() {
            redoNavigation();
        });
    
    // Reset view button
    controlsContainer.append("button")
        .attr("class", "nav-button")
        .html("🔄 Reset View")
        .style("cursor", "pointer")
        .on("click", function() {
            resetView();
        });
    
    // Add minimap
    createMinimap(container, width, height);
}

// Create minimap for navigation
function createMinimap(container, width, height) {
    // Remove existing minimap if any
    d3.select(".minimap-container").remove();
    
    // Create minimap container
    const minimapContainer = d3.select(container)
        .append("div")
        .attr("class", "minimap-container")
        .style("position", "absolute")
        .style("bottom", "10px")
        .style("left", "10px")
        .style("background-color", "rgba(255,255,255,0.8)")
        .style("border", "1px solid #ddd")
        .style("border-radius", "4px")
        .style("width", "150px")
        .style("height", "150px")
        .style("overflow", "hidden")
        .style("z-index", "100");
    
    // Add toggle button
    minimapContainer.append("button")
        .attr("class", "minimap-toggle")
        .style("position", "absolute")
        .style("top", "0")
        .style("right", "0")
        .style("width", "20px")
        .style("height", "20px")
        .style("background", "#f0f0f0")
        .style("border", "1px solid #ccc")
        .style("cursor", "pointer")
        .style("z-index", "101")
        .text("×")
        .on("click", function() {
            const minimapSvg = minimapContainer.select("svg");
            if (minimapSvg.style("display") === "none") {
                minimapSvg.style("display", "block");
                d3.select(this).text("×");
            } else {
                minimapSvg.style("display", "none");
                d3.select(this).text("▢");
            }
        });
    
    // Create minimap SVG
    const minimapSvg = minimapContainer.append("svg")
        .attr("width", 150)
        .attr("height", 150);
    
    // Create minimap group
    minimapSvg.append("g")
        .attr("class", "minimap-group")
        .attr("transform", "scale(0.2)"); // Scale for minimap
    
    // Add viewport indicator
    minimapSvg.append("rect")
        .attr("class", "viewport-indicator")
        .style("stroke", "#0076CE")
        .style("stroke-width", "2px")
        .style("fill", "none")
        .style("pointer-events", "none");
}

// Function to update the minimap
function updateMinimap() {
    if (!simulationData || !simulationData.nodes) return;
    
    // Get minimap group
    const minimapGroup = d3.select(".minimap-group");
    
    // Clear previous content
    minimapGroup.selectAll("*").remove();
    
    // Create nodes and links in minimap
    const minimapLinks = minimapGroup.selectAll(".minimap-link")
        .data(simulationData.links)
        .enter()
        .append("line")
        .attr("class", "minimap-link")
        .attr("stroke", "#999")
        .attr("stroke-width", 0.5);
    
    const minimapNodes = minimapGroup.selectAll(".minimap-node")
        .data(simulationData.nodes)
        .enter()
        .append("circle")
        .attr("class", "minimap-node")
        .attr("r", d => d.type === "llevel" ? 5 : 3)
        .attr("fill", d => {
            if (d.type === "product") {
                return d.status === "patched" ? "#28a745" : "#dc3545";
            } else if (d.type === "owner") {
                return "#ffc107";
            } else if (d.type === "llevel") {
                return "#6f42c1";
            }
            return "#999";
        });
    
    // Update positions on simulation tick
    simulation.on("tick.minimap", () => {
        minimapLinks
            .attr("x1", d => d.source.x)
            .attr("y1", d => d.source.y)
            .attr("x2", d => d.target.x)
            .attr("y2", d => d.target.y);
        
        minimapNodes
            .attr("cx", d => d.x)
            .attr("cy", d => d.y);
        
        updateViewportIndicator();
    });
}

// Function to update the viewport indicator on the minimap
function updateViewportIndicator() {
    const container = document.getElementById('clusterVisualizationContainer');
    const width = container.clientWidth;
    const height = container.clientHeight;
    
    // Calculate viewport rectangle based on current transform
    const scale = 0.2; // Minimap scale
    const viewWidth = width / currentTransform.k * scale;
    const viewHeight = height / currentTransform.k * scale;
    const viewX = -currentTransform.x / currentTransform.k * scale;
    const viewY = -currentTransform.y / currentTransform.k * scale;
    
    // Update viewport indicator
    d3.select(".viewport-indicator")
        .attr("x", viewX)
        .attr("y", viewY)
        .attr("width", viewWidth)
        .attr("height", viewHeight);
}

// Function to load data from API
function loadVisualizationData() {
    // Show loading spinner
    showLoading(true);
    
    // Get app owner from URL or element
    const ownerElement = document.getElementById('clusterOwnerName');
    const appOwner = ownerElement ? ownerElement.textContent.trim() : 'All';
    const appOwnerParam = appOwner === 'All' ? '' : encodeURIComponent(appOwner);
    
    // Fetch data from API
    fetch(`/api/clusters?app_owner=${appOwnerParam}`)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            // Store data and create visualization
            simulationData = data;
            createVisualization(data);
            updateMetrics(data);
            generateInsights(data);
            showLoading(false);
        })
        .catch(error => {
            console.error("Error loading visualization data:", error);
            showError(`Failed to load data: ${error.message}`);
            showLoading(false);
        });
}

// Helper function to calculate node radius
function getNodeRadius(d) {
    if (d.type === "llevel") return 18;
    if (d.type === "owner") return 10 + Math.min(d.count / 10, 8);
    if (d.type === "product") return 8 + Math.min(d.count / 5, 12);
    return 8;
}

// Function for drag behavior
function drag(simulation) {
    function dragstarted(event) {
        if (!event.active) simulation.alphaTarget(0.3).restart();
        event.subject.fx = event.subject.x;
        event.subject.fy = event.subject.y;
    }
    
    function dragged(event) {
        event.subject.fx = event.x;
        event.subject.fy = event.y;
    }
    
    function dragended(event) {
        if (!event.active) simulation.alphaTarget(0);
        event.subject.fx = null;
        event.subject.fy = null;
    }
    
    return d3.drag()
        .on("start", dragstarted)
        .on("drag", dragged)
        .on("end", dragended);
}

// Function to show/hide loading spinner
function showLoading(show) {
    const container = document.getElementById('clusterVisualizationContainer');
    if (!container) return;
    const loadingElements = container.querySelectorAll('.loading');
    if (show) {
        loadingElements.forEach(el => {
            el.style.display = 'flex';
        });
    } else {
        // Hide the loading elements to reveal the floating navigation toolbar and visualization
        loadingElements.forEach(el => {
            el.style.display = 'none';
        });
    }
}

// Function to show error
function showError(message) {
    const container = document.getElementById('clusterVisualizationContainer');
    if (container) {
        container.innerHTML = `<div class="error-message">${message}</div>`;
    } else {
        console.error('Error container not found:', message);
    }
}

// Function to generate insights (placeholder)
function generateInsights(data) {
    const insightsContainer = document.getElementById('insights-container');
    if (!insightsContainer) return;
    
    insightsContainer.innerHTML = '<div class="insight-item"><div class="insight-name">Product Correlation Patterns</div><div class="insight-description">The visualization shows clear patterns between product usage across organizational levels. Products in the same cluster often require similar remediation approaches.</div></div>';
}

// Function to update metrics
function updateMetrics(data) {
    if (!data || !data.nodes) return;
    
    const productNodes = data.nodes.filter(d => d.type === "product");
    const ownerNodes = data.nodes.filter(d => d.type === "owner");
    const remediationNodes = data.nodes.filter(d => d.type === "product" && d.status === "unpatched");
    
    document.getElementById('productCount').textContent = productNodes.length;
    document.getElementById('ownerCount').textContent = ownerNodes.length;
    document.getElementById('remediationCount').textContent = remediationNodes.length;
    document.getElementById('avgSuccess').textContent = '78.5%';
}

// Function to update the visualization view
function updateVisualizationView() {
    // This function will be implemented in the actual visualization code
    console.log("View updated to:", currentView);
}

// Function to filter visualization
function filterVisualization() {
    // This function will be implemented in the actual visualization code
    console.log("Filter applied:", statusFilter);
}

// Variable to track navigation history state
let isNavigatingHistory = false;

// Function to add current transform to navigation history
function addToHistory() {
    // If we're in the middle of the history array, truncate the future states
    if (historyIndex >= 0 && historyIndex < transformHistory.length - 1) {
        transformHistory = transformHistory.slice(0, historyIndex + 1);
    }
    
    // Add current transform to history
    transformHistory.push({...currentTransform});
    historyIndex = transformHistory.length - 1;
    
    // Update button states
    updateHistoryButtonStates();
}

// Function to undo navigation (go back in history)
function undoNavigation() {
    if (historyIndex <= 0) return;
    
    isNavigatingHistory = true;
    historyIndex--;
    const prevTransform = transformHistory[historyIndex];
    applyTransformFromHistory(prevTransform);
    
    // Update button states
    updateHistoryButtonStates();
}

// Function to redo navigation (go forward in history)
function redoNavigation() {
    if (historyIndex >= transformHistory.length - 1) return;
    
    isNavigatingHistory = true;
    historyIndex++;
    const nextTransform = transformHistory[historyIndex];
    applyTransformFromHistory(nextTransform);
    
    // Update button states
    updateHistoryButtonStates();
}

// Function to apply a transform from history
function applyTransformFromHistory(transform) {
    // Update current transform
    currentTransform = {...transform};
    
    // Apply transform to the SVG group
    svgGroup.transition()
        .duration(300)
        .attr("transform", `translate(${transform.x},${transform.y}) scale(${transform.k}) rotate(${transform.rotation})`);
    
    // Update minimap viewport indicator
    updateViewportIndicator();
    
    // Reset navigation flag after transition
    setTimeout(() => { isNavigatingHistory = false; }, 350);
}

// Function to update history button states
function updateHistoryButtonStates() {
    const undoButton = d3.select(".navigation-controls button").filter(function() {
        return d3.select(this).html() === "↩";
    });
    
    const redoButton = d3.select(".navigation-controls button").filter(function() {
        return d3.select(this).html() === "↪";
    });
    
    undoButton.attr("disabled", historyIndex <= 0 ? true : null)
        .style("opacity", historyIndex <= 0 ? 0.5 : 1);
        
    redoButton.attr("disabled", historyIndex >= transformHistory.length - 1 ? true : null)
        .style("opacity", historyIndex >= transformHistory.length - 1 ? 0.5 : 1);
}

// Function to reset view to initial state
function resetView() {
    isNavigatingHistory = true;
    
    // Reset the transform
    currentTransform = { k: 1, x: 0, y: 0, rotation: 0 };
    
    // Apply the reset transform with transition
    svgGroup.transition()
        .duration(500)
        .attr("transform", `translate(0,0) scale(1) rotate(0)`)
        .on("end", function() {
            isNavigatingHistory = false;
            // Reset zoom behavior
            svg.call(zoomBehavior.transform, d3.zoomIdentity);
            // Add to history
            addToHistory();
        });
        
    // Update minimap
    updateViewportIndicator();
}

// Function to rotate visualization
function rotateVisualization(degrees) {
    // Add current rotation
    currentTransform.rotation = (currentTransform.rotation || 0) + degrees;
    
    // Apply rotation with transition
    svgGroup.transition()
        .duration(300)
        .attr("transform", `translate(${currentTransform.x},${currentTransform.y}) scale(${currentTransform.k}) rotate(${currentTransform.rotation})`);
    
    // Add to history if not already navigating history
    if (!isNavigatingHistory) {
        addToHistory();
    }
}

// Function to create the visualization
function createVisualization(data) {
    if (!data || !data.nodes || !data.links || data.nodes.length === 0) {
        showError("No data available for visualization.");
        return;
    }
    
    const container = document.getElementById('enhancedVisualizationContainer');
    const width = container.clientWidth;
    const height = container.clientHeight;
    
    // Initialize navigation history
    transformHistory = [{k: 1, x: 0, y: 0, rotation: 0}];
    historyIndex = 0;
    currentTransform = { k: 1, x: 0, y: 0, rotation: 0 };
    updateHistoryButtonStates();
    
    // Define color scales
    const nodeColorScale = d3.scaleOrdinal()
        .domain(["product_patched", "product_unpatched", "owner", "llevel"])
        .range(["#28a745", "#dc3545", "#ffc107", "#6f42c1"]);
    
    // Define L-level specific colors (different shades of purple)
    const levelColorScale = d3.scaleOrdinal()
        .domain(["L4", "L5", "L6/L7"])
        .range(["#9d6dd7", "#7952b3", "#563d7c"]);
    
    // Setup simulation
    simulation = d3.forceSimulation(data.nodes)
        .force("link", d3.forceLink(data.links)
            .id(d => d.id)
            .distance(d => {
                // Different link distances based on relationship type
                if (d.type === "co_occurs") return 50;
                if (d.type === "owned_by") return 120;
                if (d.type === "managed_by") return 180;
                if (d.type === "grouped_under") return 100;
                return 150;
            })
            .strength(d => {
                // Different link strengths based on relationship type
                if (d.type === "co_occurs") return 0.7;
                if (d.type === "owned_by") return 0.5;
                if (d.type === "managed_by") return 0.4;
                return 0.3;
            })
        )
        .force("charge", d3.forceManyBody()
            .strength(d => {
                // Stronger repulsion for larger nodes
                if (d.type === "llevel") return -500;
                if (d.type === "owner") return -300;
                return -200;
            })
        )
        .force("center", d3.forceCenter(width / 2, height / 2))
        .force("collide", d3.forceCollide().radius(d => getNodeRadius(d) + 5));
    
    // Create hierarchical structure based on current view
    updateForces();
    
    // Clear previous visualization elements
    svgGroup.selectAll("*").remove();
    
    // Link elements
    const link = svgGroup.append("g")
        .attr("class", "links")
        .attr("stroke-opacity", 0.6)
        .selectAll("line")
        .data(data.links)
        .join("line")
        .attr("class", "link")
        .attr("stroke-width", d => Math.sqrt(d.value) * 1.5)
        .attr("stroke", d => {
            if (d.type === "co_occurs") return "#999";
            if (d.type === "owned_by") return "#ffc107";
            if (d.type === "managed_by") return "#6f42c1";
            if (d.type === "grouped_under") return "#007bff";
            return "#666";
        });
    
    // Node group elements
    const node = svgGroup.append("g")
        .attr("class", "nodes")
        .selectAll(".node")
        .data(data.nodes)
        .join("g")
        .attr("class", "node")
        .call(drag(simulation))
        .on("mouseover", handleNodeMouseOver)
        .on("mouseout", handleNodeMouseOut);
    
    // Add node shapes based on type
    node.each(function(d) {
        const nodeGroup = d3.select(this);
        
        if (d.type === "product") {
            // Circle for products with different colors based on status
            nodeGroup.append("circle")
                .attr("r", getNodeRadius(d))
                .attr("fill", d.status === "patched" ? nodeColorScale("product_patched") : nodeColorScale("product_unpatched"))
                .attr("stroke", "#fff")
                .attr("stroke-width", 2);
                
            // Add pulsing effect to unpatched products
            if (d.status === "unpatched") {
                nodeGroup.append("circle")
                    .attr("r", getNodeRadius(d) + 5)
                    .attr("fill", "none")
                    .attr("stroke", "#dc3545")
                    .attr("stroke-width", 1)
                    .attr("opacity", 0.5)
                    .attr("class", "pulse-circle");
            }
        } else if (d.type === "owner") {
            // Rectangle for owners
            const size = getNodeRadius(d) * 1.8;
            nodeGroup.append("rect")
                .attr("width", size)
                .attr("height", size)
                .attr("x", -size/2)
                .attr("y", -size/2)
                .attr("fill", nodeColorScale("owner"))
                .attr("stroke", "#fff")
                .attr("stroke-width", 2)
                .attr("rx", 4)
                .attr("ry", 4);
        } else if (d.type === "llevel") {
            // Diamond for L-levels
            const size = getNodeRadius(d);
            nodeGroup.append("path")
                .attr("d", d3.symbol().type(d3.symbolDiamond).size(size * 20))
                .attr("fill", levelColorScale(d.name))
                .attr("stroke", "#fff")
                .attr("stroke-width", 2);
        }
        
        // Add text labels
        nodeGroup.append("text")
            .attr("text-anchor", "middle")
            .attr("dy", d.type === "product" ? getNodeRadius(d) + 12 : 4)
            .attr("font-size", d.type === "llevel" ? "14px" : "10px")
            .attr("fill", d.type === "llevel" ? "#fff" : "#333")
            .attr("pointer-events", "none")
            .text(d.name);
    });
    
    // Create legend
    createLegend();
    
    // Simulation tick function
    simulation.on("tick", () => {
        link
            .attr("x1", d => d.source.x)
            .attr("y1", d => d.source.y)
            .attr("x2", d => d.target.x)
            .attr("y2", d => d.target.y);
        
        node.attr("transform", d => `translate(${d.x},${d.y})`);
    });
    
    // Handle node hover
    function handleNodeMouseOver(event, d) {
        // Show tooltip with detailed info
        let tooltipContent = `<strong>${d.name}</strong><br>`;
        if (d.type === "product") {
            tooltipContent += `Type: Product<br>`;
            tooltipContent += `Status: ${d.status === "patched" ? "Patched/Compliant" : "Needs remediation"}<br>`;
            tooltipContent += `Executions: ${d.count || 0}<br>`;
            tooltipContent += `Success rate: ${d.success_rate ? d.success_rate.toFixed(1) + '%' : 'N/A'}<br>`;
        } else if (d.type === "owner") {
            tooltipContent += `Type: App Owner<br>`;
            tooltipContent += `Associated products: ${d.count || 0}<br>`;
            tooltipContent += `Main L-Level: ${d.l_level || "Unknown"}<br>`;
        } else if (d.type === "llevel") {
            tooltipContent += `Type: Organizational Level<br>`;
            tooltipContent += `Represents: ${getLevelDescription(d.name)}<br>`;
        }
        
        tooltip
            .style("visibility", "visible")
            .style("left", (event.pageX + 10) + "px")
            .style("top", (event.pageY - 10) + "px")
            .html(tooltipContent);
            
        // Highlight connections
        highlightConnections(d);
    }
    
    // Handle node mouseout
    function handleNodeMouseOut() {
        // Hide tooltip
        tooltip.style("visibility", "hidden");
        // Reset highlighting
        resetHighlighting();
    }
}

// Helper function to get L-level description
function getLevelDescription(level) {
    switch(level) {
        case "L4": return "Management Level";
        case "L5": return "Team Level";
        case "L6/L7": return "Implementation Level";
        default: return "Organizational Level";
    }
}

// Function to highlight connections
function highlightConnections(d) {
    // Dim all nodes and links
    svgGroup.selectAll(".node").style("opacity", 0.3);
    svgGroup.selectAll(".link").style("opacity", 0.1);
    
    // Get connected node IDs
    const connectedNodes = new Set();
    connectedNodes.add(d.id);
    
    simulationData.links.forEach(l => {
        const sourceId = typeof l.source === 'object' ? l.source.id : l.source;
        const targetId = typeof l.target === 'object' ? l.target.id : l.target;
        
        if (sourceId === d.id || targetId === d.id) {
            connectedNodes.add(sourceId);
            connectedNodes.add(targetId);
        }
    });
    
    // Highlight connected nodes and links
    svgGroup.selectAll(".node")
        .filter(n => connectedNodes.has(n.id))
        .style("opacity", 1);
        
    svgGroup.selectAll(".link")
        .filter(l => {
            const sourceId = typeof l.source === 'object' ? l.source.id : l.source;
            const targetId = typeof l.target === 'object' ? l.target.id : l.target;
            return connectedNodes.has(sourceId) && connectedNodes.has(targetId);
        })
        .style("opacity", 1)
        .attr("stroke-width", l => Math.sqrt(l.value) * 2);
}

// Function to reset highlighting
function resetHighlighting() {
    svgGroup.selectAll(".node").style("opacity", 1);
    svgGroup.selectAll(".link")
        .style("opacity", 0.6)
        .attr("stroke-width", d => Math.sqrt(d.value) * 1.5);
}

// Function to update forces based on view type
function updateForces() {
    if (!simulation) return;
    
    const container = document.getElementById('enhancedVisualizationContainer');
    const width = container.clientWidth;
    const height = container.clientHeight;
    
    // Remove existing forces
    simulation.force("x", null)
            .force("y", null);
    
    // Add new forces based on view type
    if (currentView === "llevel") {
        // L-Level hierarchy view
        simulation.force("x", d3.forceX().x(d => {
            if (d.type === "llevel") {
                if (d.name === "L4") return width * 0.25;
                if (d.name === "L5") return width * 0.5;
                if (d.name === "L6/L7") return width * 0.75;
                return width * 0.5;
            } else if (d.type === "owner") {
                // Position owners based on their L-level
                if (d.l_level === "L4") return width * 0.25;
                if (d.l_level === "L5") return width * 0.5;
                if (d.l_level === "L6/L7") return width * 0.75;
                return width * 0.5;
            }
            return width * 0.5;
        }).strength(d => {
            if (d.type === "llevel") return 0.4;
            if (d.type === "owner") return 0.2;
            return 0.05;
        }));
        
        simulation.force("y", d3.forceY().y(d => {
            if (d.type === "llevel") return height * 0.2;
            if (d.type === "owner") return height * 0.5;
            return height * 0.7;
        }).strength(d => {
            if (d.type === "llevel") return 0.2;
            if (d.type === "owner") return 0.1;
            return 0.05;
        }));
    } else if (currentView === "owner") {
        // Owner-centric view
        simulation.force("x", d3.forceX().x(d => {
            if (d.type === "owner") return width * 0.3;
            if (d.type === "product") return width * 0.7;
            if (d.type === "llevel") return width * 0.1;
            return width * 0.5;
        }).strength(d => {
            if (d.type === "owner") return 0.3;
            if (d.type === "llevel") return 0.4;
            return 0.05;
        }));
        
        simulation.force("y", d3.forceY().y(d => {
            if (d.type === "owner") return height * 0.5;
            if (d.type === "llevel") return height * 0.2;
            return height * 0.6;
        }).strength(d => {
            if (d.type === "owner" || d.type === "llevel") return 0.2;
            return 0.05;
        }));
    } else {
        // Product cluster view
        simulation.force("x", d3.forceX().x(width / 2).strength(0.05));
        simulation.force("y", d3.forceY().y(height / 2).strength(0.05));
    }
    
    // Restart simulation with a higher alpha to reorganize
    simulation.alpha(0.3).restart();
}

// Function to create legend
function createLegend() {
    // Remove any existing legend
    d3.select(".legend-container").remove();
    
    const container = d3.select("#clusterVisualizationContainer");
    const legend = container.append("div")
        .attr("class", "legend-container");
        
    legend.append("div")
        .attr("class", "legend-title")
        .style("font-weight", "bold")
        .style("margin-bottom", "8px")
        .text("Legend");
        
    // Node types
    const nodeTypes = [
        { label: "Product (patched/compliant)", color: "#28a745", shape: "circle" },
        { label: "Product (needs remediation)", color: "#dc3545", shape: "circle" },
        { label: "App Owner", color: "#ffc107", shape: "square" },
        { label: "L4 Level", color: "#9d6dd7", shape: "diamond" },
        { label: "L5 Level", color: "#7952b3", shape: "diamond" },
        { label: "L6/L7 Level", color: "#563d7c", shape: "diamond" }
    ];
    
    // Add node type legend items
    nodeTypes.forEach(item => {
        const itemDiv = legend.append("div")
            .attr("class", "legend-item");
            
        if (item.shape === "circle") {
            itemDiv.append("div")
                .attr("class", "legend-circle")
                .style("background-color", item.color);
        } else if (item.shape === "square") {
            itemDiv.append("div")
                .attr("class", "legend-square")
                .style("background-color", item.color);
        } else if (item.shape === "diamond") {
            itemDiv.append("div")
                .attr("class", "legend-diamond")
                .style("background-color", item.color);
        }
        
        itemDiv.append("div")
            .text(item.label);
    });
    
    // Link types
    const linkTypes = [
        { label: "Product co-occurrence", color: "#999" },
        { label: "Owner relationship", color: "#ffc107" },
        { label: "L-Level relationship", color: "#6f42c1" }
    ];
    
    // Add separator
    legend.append("hr").style("margin", "8px 0");
    
    // Add link type legend items
    linkTypes.forEach(item => {
        const itemDiv = legend.append("div")
            .attr("class", "legend-item");
            
        itemDiv.append("div")
            .attr("class", "legend-line")
            .style("background-color", item.color);
            
        itemDiv.append("div")
            .text(item.label);
    });
}
