include:
  - project: devops/job-templates
    ref: v3.0.0
    file: artifactory-jobs.yml
  - project: devops/job-templates
    ref: v3.0.0
    file: python-jobs.yml
  - project: devops/job-templates
    ref: v3.0.0
    file: change-jobs.yml
  - project: devops/job-templates
    ref: v3.0.0
    file: cicd-stages.yml
  - project: devops/job-templates
    ref: v3.0.0
    file: security-jobs.yml
  - project: devops/job-templates
    ref: v3.0.0
    file: deploy-jobs.yml
  - project: devops/cicd-constructs
    ref: v3.0.0
    file: cicd-constructs.yml

variables:
  CHANGE_DURATION: 30
  ACCESS_TOKEN: kv/ARTIFACTORY
  VAULT_NAMESPACE: '1007693'
  VAULT_ADDR: https://vault.dell.com
  APP_NAME: vsr-recommendation-tool
  TEMP_APP_NAME: vsr-recommendation-tool-temp
  BROWN_APP_NAME: vsr-recommendation-tool-brown
  APP_NAME_PERF: vsr-recommendation-tool-perf
  TARGET: .
  REQ_FILE: requirements.txt
  PCF_MF_FILES: manifest.yml
  DOMAIN_NP: ausvdc02.pcf.dell.com
  GTM_DOMAIN: ausmpc.pcf.dell.com
  DOMAIN_PRI: ausmpc01.pcf.dell.com
  DOMAIN_SEC: ausmsc01.pcf.dell.com
  RFC_IMAGE: harbor.dell.com/devops-images/snowctl-devops:pro_v4.8.0

compile-package:
  extends: .compile-package-python
  variables:
    REQ_FILE: requirements.txt
  rules:
    - if: $CI_COMMIT_BRANCH =~ /^([fF]eature|[rR]elease).*$/
    - if: $CI_COMMIT_BRANCH =~ /^([Mm]aster|[Mm]ain).*$/

unit-test:
  extends: .unit-test-python
  variables:
    UNIT_TEST_FILE: unit-test/serverTest.py
    REQ_FILE: requirements.txt
  rules:
    - if: $CI_COMMIT_BRANCH =~ /^([fF]eature).*$/
      allow_failure: false
    - if: $CI_COMMIT_BRANCH =~ /^([Mm]aster|[Mm]ain).*$/
      allow_failure: false

inclusive-lang:
  extends: .inclusive_language
  rules:
    - if: $CI_COMMIT_BRANCH =~ /^([fF]eature|[rR]elease).*$/
    - if: $CI_COMMIT_BRANCH =~ /^([Mm]aster|[Mm]ain).*$/

code-quality:
  variables:
    SONAR_PROJECT_NAME: $CI_PROJECT_NAME
    COVERAGE_FILE: unit-test/coverage.xml
    UNIT_TEST_FILE: unit-test/serverTest.py
    REQ_FILE: requirements.txt
    SONAR_LANG: python
    SONAR_IMAGE_TAG: $SONAR_IMAGE
  rules:
    - if: $CI_COMMIT_BRANCH =~ /^([fF]eature).*$/
      allow_failure: false
    - if: $CI_COMMIT_BRANCH =~ /^([Mm]aster|[Mm]ain).*$/
      allow_failure: false
  extends: .code-quality

xray_scan:
  extends: .xray-scan
  variables:
    ARTIFACT_LANG: default
    ARTIFACT_SECRET_PATH: kv/ARTIFACTORY
  allow_failure: false

k8s-kobace-deploy-dev:
  extends: .deploy-k8s-kobace
  variables:
    VAULT_CONFIG_PATH: kv/KOB/DEV
    TARGET_K8S_OBJECTS: manifest.yml
  rules:
    - if: $CI_COMMIT_BRANCH =~ /^([fF]eature).*$/
    - if: $CI_COMMIT_BRANCH =~ /^([Mm]aster|[Mm]ain).*$/

Dynamic-scan:
  extends: .dast
  variables:
    DAST_FOLDER_ITEM_ID: xxx
  rules:
    - if: $CI_COMMIT_BRANCH =~ /^([Re]ealse|[Dd]evelop).*$/
      allow_failure: false

publish-package:
  extends: .publish-artifact
  rules:
    - if: $CI_COMMIT_BRANCH =~ /^([Mm]aster|[Mm]ain).*$/

create-rfc:
  stage: rfc-submit
  extends: .create-rfc-standard
  image: $SNOWCTL_IMAGE
  rules:
    - if: $CI_COMMIT_TAG
  environment:
    name: rfc
  script:
    - curl -s $BP_VERSION_SH > bp_version.sh;source bp_version.sh
    - export VAULT_ADDR=$PROD_VAULT_ADDR; echo "Vault address --> $VAULT_ADDR"
    - >-
      echo -e "\x1b[36;1mRunning \x1b[35;1m[$CI_JOB_STAGE]\x1b[36;1m stage with
      $CI_JOB_IMAGE ...\e[0m"
    - >-
      snowctl create change standard-change.json --project-id=$DB_PROJ_ID
      --duration=30,m
    - >-
      snowctl add note "Change Request created by GitLab Job $CI_JOB_URL"
      --change
  variables: {}

retrieve-artifact:
  extends: .retrieve-artifact
  rules:
    - if: $CI_COMMIT_BRANCH =~ /^([Mm]aster|[Mm]ain).*$/

k8s-deploy-PC1:
  extends: .deploy-k8s-kobace-prod
  variables:
    VAULT_CONFIG_PATH: kv/KOB/PC1
    TARGET_K8S_OBJECTS: manifest.yml
  rules:
    - if: $CI_COMMIT_TAG

k8s-deploy-S3B:
  extends: .deploy-k8s-kobace-prod
  variables:
    VAULT_CONFIG_PATH: kv/KOB/S3B
    TARGET_K8S_OBJECTS: manifest.yml
  rules:
    - if: $CI_COMMIT_TAG

closed-skip:
  extends: .closed-skip
  rules:
    - if: $CI_COMMIT_TAG || $CI_PIPELINE_SOURCE == "pipeline"
      when: manual
      allow_failure: true

update-rfc-deploy:
  extends: .update-rfc-deploy
  environment:
    name: rfc
  image: $SNOWCTL_IMAGE
  rules:
    - if: $CI_COMMIT_TAG

update-rfc-validate:
  extends: .update-rfc-validate
  environment:
    name: rfc
  image: $SNOWCTL_IMAGE
  rules:
    - if: $CI_COMMIT_TAG

