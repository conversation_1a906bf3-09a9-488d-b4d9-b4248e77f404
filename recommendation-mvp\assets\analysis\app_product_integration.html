<div class="container"> <h1>App & Product Data Integration</h1>
    <p>This analysis extracts and correlates product details (names, apps, metrics) from referenced sources, verifies hierarchical relationships between product recommendations, organizational levels, and app ownership, and flags inconsistencies in the data.</p>

    <h2>Hierarchical Breakdown with App/Product Context</h2>
    <table>
        <thead>
            <tr>
                <th>Level</th>
                <th>Product</th>
                <th>App</th>
                <th>Success Rate</th>
                <th>Owner</th>
                <th>Remediation Steps</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td><span class="badge badge-primary">L4</span></td>
                <td>TLS Standards</td>
                <td>All</td>
                <td class="success-rate high">85%</td>
                <td>Security Team</td>
                <td>Disable TLS 1.0/1.1</td>
            </tr>
            <tr>
                <td><span class="badge badge-success">L5</span></td>
                <td>IIS</td>
                <td>HR Portal</td>
                <td class="success-rate high">72%</td>
                <td>Web Team</td>
                <td>Patch CVE-2023-1234</td>
            </tr>
            <tr>
                <td><span class="badge badge-success">L5</span></td>
                <td>Apache</td>
                <td>CRM System</td>
                <td class="success-rate medium">58%</td>
                <td>Platform Team</td>
                <td>Install ModSecurity</td>
            </tr>
            <tr>
                <td><span class="badge badge-warning">L6</span></td>
                <td>OpenSSL</td>
                <td>Payment API</td>
                <td class="success-rate medium">53%</td>
                <td>DevOps</td>
                <td>Upgrade to 3.0.7</td>
            </tr>
            <tr>
                <td><span class="badge badge-warning">L6</span></td>
                <td>IIS</td>
                <td>HR Portal</td>
                <td class="success-rate high">69%</td>
                <td>App Team</td>
                <td>Remove Default Welcome Page</td>
            </tr>
            <tr>
                <td><span class="badge badge-warning">L6</span></td>
                <td>Nginx</td>
                <td>API Gateway</td>
                <td class="success-rate high">67%</td>
                <td>Platform Team</td>
                <td>Enable HTTP/2</td>
            </tr>
        </tbody>
    </table>

    <div class="inconsistency-section">
        <div class="inconsistency-title">Data Inconsistencies & Gaps</div>
        <ul>
            <li><strong>Missing L6/L7 data for "Nginx HTTP/2 Support"</strong> - Implementation details not available for 3 applications</li>
            <li><strong>App-to-Product mapping gaps</strong> - Some applications have incomplete product associations</li>
            <li><strong>Success rate calculation discrepancies</strong> - L5 shows 49% for IIS patches while L6 shows 53% for the same product</li>
            <li><strong>Duplicate Apache entries</strong> - Multiple similar recommendations should be consolidated</li>
        </ul>
    </div>

    <h2>App & Product Thresholds</h2>
    <ul>
        <li><strong>IIS Default Welcome Page:</strong> Must show 0 detections for compliance</li>
        <li><strong>TLS 1.0:</strong> Must be disabled in all apps</li>
        <li><strong>Apache ModSecurity:</strong> Required for all public-facing web applications</li>
        <li><strong>OpenSSL:</strong> Version 3.0.7+ required for all payment processing systems</li>
    </ul>

    <h2>Application Cards with Product Context</h2>
    <div class="app-cards">
        <div class="app-card">
            <div class="app-header">
                <div class="app-name">HR Portal</div>
                <span class="badge badge-success">High Priority</span>
            </div>
            <div class="app-owner">
                Web Team <span class="level-tag">L5</span>
            </div>
            <div class="app-metrics">
                <div class="metric-box">
                    <div class="metric-name">Vulnerabilities Remediated</div>
                    <div class="metric-value">24</div>
                </div>
                <div class="metric-box">
                    <div class="metric-name">Servers</div>
                    <div class="metric-value">8</div>
                </div>
            </div>
            <div class="product-section">
                <div class="product-header">Product Usage:</div>
                <div class="product-list">
                    <div class="product-tag">IIS <span class="success">72%</span></div>
                    <div class="product-tag">TLS/SSL <span class="success">85%</span></div>
                    <div class="product-tag">Security Headers <span class="success">82%</span></div>
                </div>
            </div>
            <div class="remediation-steps">
                <div class="remediation-title">Remediation Steps:</div>
                <ol>
                    <li>Patch IIS vulnerabilities (CVE-2023-1234)</li>
                    <li>Remove Default Welcome Page</li>
                    <li>Implement security headers</li>
                </ol>
            </div>
        </div>

        <div class="app-card">
            <div class="app-header">
                <div class="app-name">Payment API</div>
                <span class="badge badge-success">High Priority</span>
            </div>
            <div class="app-owner">
                DevOps <span class="level-tag">L6</span>
            </div>
            <div class="app-metrics">
                <div class="metric-box">
                    <div class="metric-name">Vulnerabilities Remediated</div>
                    <div class="metric-value">18</div>
                </div>
                <div class="metric-box">
                    <div class="metric-name">Servers</div>
                    <div class="metric-value">6</div>
                </div>
            </div>
            <div class="product-section">
                <div class="product-header">Product Usage:</div>
                <div class="product-list">
                    <div class="product-tag">OpenSSL <span class="success">53%</span></div>
                    <div class="product-tag">TLS/SSL <span class="success">85%</span></div>
                    <div class="product-tag">Nginx <span class="success">67%</span></div>
                </div>
            </div>
            <div class="remediation-steps">
                <div class="remediation-title">Remediation Steps:</div>
                <ol>
                    <li>Upgrade OpenSSL to 3.0.7</li>
                    <li>Disable TLS 1.0/1.1</li>
                    <li>Enable HTTP/2 in Nginx</li>
                </ol>
            </div>
        </div>

        <div class="app-card">
            <div class="app-header">
                <div class="app-name">CRM System</div>
                <span class="badge badge-warning">Medium Priority</span>
            </div>
            <div class="app-owner">
                Platform Team <span class="level-tag">L5</span>
            </div>
            <div class="app-metrics">
                <div class="metric-box">
                    <div class="metric-name">Vulnerabilities Remediated</div>
                    <div class="metric-value">12</div>
                </div>
                <div class="metric-box">
                    <div class="metric-name">Servers</div>
                    <div class="metric-value">4</div>
                </div>
            </div>
            <div class="product-section">
                <div class="product-header">Product Usage:</div>
                <div class="product-list">
                    <div class="product-tag">Apache <span class="success">58%</span></div>
                    <div class="product-tag">ModSecurity <span class="success">78%</span></div>
                    <div class="product-tag">TLS/SSL <span class="success">85%</span></div>
                </div>
            </div>
            <div class="remediation-steps">
                <div class="remediation-title">Remediation Steps:</div>
                <ol>
                    <li>Install ModSecurity</li>
                    <li>Configure security rules</li>
                    <li>Disable TLS 1.0/1.1</li>
                </ol>
            </div>
        </div>
    </div>

    <h2>Actionable Insights</h2>
    <ol>
        <li><strong>Merge duplicate Apache entries</strong> in L5 level to improve consistency</li>
        <li><strong>Add app-filter dropdowns</strong> for cross-level analysis</li>
        <li><strong>Standardize success rate calculations</strong> across levels</li>
        <li><strong>Investigate missing L6/L7 data</strong> for Nginx HTTP/2 support</li>
        <li><strong>Enhance app-to-product mapping</strong> to eliminate gaps</li>
    </ol>
</div>
