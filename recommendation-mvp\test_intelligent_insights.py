#!/usr/bin/env python3
"""
Test script for intelligent insights functionality.
Tests the new AI-driven recommendations based on owner behavior patterns and clustering.
"""

import requests
import json
import time
from typing import Dict, Any

def test_api_endpoint(url: str, params: Dict[str, str] = None) -> Dict[str, Any]:
    """Test an API endpoint and return the response."""
    try:
        response = requests.get(url, params=params, timeout=30)
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        print(f"Error testing {url}: {e}")
        return {}

def test_intelligent_insights_in_main_api():
    """Test intelligent insights in the main recommendations API."""
    print("Testing Intelligent Insights in Main API...")
    
    data = test_api_endpoint("http://127.0.0.1:5000/api/recommendations")
    
    if data:
        print("✓ Main API accessible")
        
        # Check if product_recommendations exists
        if "product_recommendations" in data:
            product_recs = data["product_recommendations"]
            print("✓ Product recommendations included in main API")
            
            # Check for intelligent recommendations
            if "intelligent_recommendations" in product_recs:
                intelligent_recs = product_recs["intelligent_recommendations"]
                print(f"✓ Found {len(intelligent_recs)} intelligent recommendations")
                
                if intelligent_recs:
                    # Analyze first recommendation
                    rec = intelligent_recs[0]
                    print(f"  - First recommendation: {rec.get('title', 'No title')}")
                    print(f"  - Priority: {rec.get('priority', 'Unknown')}")
                    print(f"  - Type: {rec.get('type', 'Unknown')}")
                    print(f"  - Confidence: {rec.get('confidence_score', 0)*100:.1f}%")
                    
                    # Check for required fields
                    required_fields = ["title", "description", "priority", "type", "confidence_score"]
                    missing_fields = [field for field in required_fields if field not in rec]
                    
                    if not missing_fields:
                        print("✓ All required fields present in recommendations")
                    else:
                        print(f"⚠️  Missing fields: {missing_fields}")
                    
                    return True
                else:
                    print("⚠️  No intelligent recommendations generated")
                    return True
            else:
                print("✗ No intelligent_recommendations field found")
                return False
        else:
            print("✗ No product_recommendations in main API response")
            return False
    else:
        print("✗ Failed to access main API")
        return False

def test_owner_behavior_insights():
    """Test owner behavior pattern analysis."""
    print("\nTesting Owner Behavior Insights...")
    
    data = test_api_endpoint("http://127.0.0.1:5000/api/recommendations")
    
    if data and "product_recommendations" in data:
        product_recs = data["product_recommendations"]
        
        if "owner_behavior_insights" in product_recs:
            insights = product_recs["owner_behavior_insights"]
            print(f"✓ Found {len(insights)} owner behavior insights")
            
            if insights:
                # Analyze insights
                risk_levels = {}
                for insight in insights:
                    risk_level = insight.get("risk_level", "unknown")
                    risk_levels[risk_level] = risk_levels.get(risk_level, 0) + 1
                
                print("✓ Risk level distribution:")
                for level, count in risk_levels.items():
                    print(f"    {level}: {count}")
                
                # Check first insight details
                first_insight = insights[0]
                print(f"  - First insight owner: {first_insight.get('owner', 'Unknown')}")
                print(f"  - Consistency score: {first_insight.get('consistency_score', 0):.3f}")
                print(f"  - High performing products: {len(first_insight.get('high_performing_products', []))}")
                print(f"  - Low performing products: {len(first_insight.get('low_performing_products', []))}")
                
                return True
            else:
                print("⚠️  No owner behavior insights generated")
                return True
        else:
            print("✗ No owner_behavior_insights field found")
            return False
    else:
        print("✗ Failed to get product recommendations")
        return False

def test_product_cluster_insights():
    """Test product cluster pattern analysis."""
    print("\nTesting Product Cluster Insights...")
    
    data = test_api_endpoint("http://127.0.0.1:5000/api/recommendations")
    
    if data and "product_recommendations" in data:
        product_recs = data["product_recommendations"]
        
        if "product_cluster_insights" in product_recs:
            insights = product_recs["product_cluster_insights"]
            print(f"✓ Found {len(insights)} product cluster insights")
            
            if insights:
                # Analyze cluster patterns
                success_patterns = {}
                for insight in insights:
                    pattern = insight.get("success_pattern", "unknown")
                    success_patterns[pattern] = success_patterns.get(pattern, 0) + 1
                
                print("✓ Success pattern distribution:")
                for pattern, count in success_patterns.items():
                    print(f"    {pattern}: {count}")
                
                # Check first insight details
                first_insight = insights[0]
                print(f"  - First cluster: {first_insight.get('cluster_id', 'Unknown')}")
                print(f"  - Dominant products: {len(first_insight.get('dominant_products', []))}")
                print(f"  - Product combinations: {len(first_insight.get('recommended_product_combinations', []))}")
                print(f"  - Outlier owners: {len(first_insight.get('outlier_owners', []))}")
                
                return True
            else:
                print("⚠️  No product cluster insights generated")
                return True
        else:
            print("✗ No product_cluster_insights field found")
            return False
    else:
        print("✗ Failed to get product recommendations")
        return False

def test_recommendation_types():
    """Test different types of intelligent recommendations."""
    print("\nTesting Recommendation Types...")
    
    data = test_api_endpoint("http://127.0.0.1:5000/api/recommendations")
    
    if data and "product_recommendations" in data:
        product_recs = data["product_recommendations"]
        
        if "intelligent_recommendations" in product_recs:
            recommendations = product_recs["intelligent_recommendations"]
            
            # Count recommendation types
            type_counts = {}
            priority_counts = {}
            
            for rec in recommendations:
                rec_type = rec.get("type", "unknown")
                priority = rec.get("priority", "unknown")
                
                type_counts[rec_type] = type_counts.get(rec_type, 0) + 1
                priority_counts[priority] = priority_counts.get(priority, 0) + 1
            
            print("✓ Recommendation type distribution:")
            for rec_type, count in type_counts.items():
                print(f"    {rec_type}: {count}")
            
            print("✓ Priority distribution:")
            for priority, count in priority_counts.items():
                print(f"    {priority}: {count}")
            
            return True
        else:
            print("✗ No intelligent recommendations found")
            return False
    else:
        print("✗ Failed to get data")
        return False

def test_performance():
    """Test performance of intelligent insights generation."""
    print("\nTesting Performance...")
    
    start_time = time.time()
    data = test_api_endpoint("http://127.0.0.1:5000/api/recommendations")
    end_time = time.time()
    
    response_time = end_time - start_time
    print(f"✓ API response time: {response_time:.2f} seconds")
    
    if response_time < 5.0:
        print("✓ Performance acceptable (< 5 seconds)")
        return True
    elif response_time < 10.0:
        print("⚠️  Performance marginal (5-10 seconds)")
        return True
    else:
        print("✗ Performance poor (> 10 seconds)")
        return False

def main():
    """Run all intelligent insights tests."""
    print("=" * 60)
    print("INTELLIGENT INSIGHTS TEST SUITE")
    print("=" * 60)
    
    tests = [
        test_intelligent_insights_in_main_api,
        test_owner_behavior_insights,
        test_product_cluster_insights,
        test_recommendation_types,
        test_performance
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
                print("✓ PASSED\n")
            else:
                print("✗ FAILED\n")
        except Exception as e:
            print(f"✗ ERROR: {e}\n")
    
    print("=" * 60)
    print(f"RESULTS: {passed}/{total} tests passed")
    print("=" * 60)
    
    if passed == total:
        print("🎉 All tests passed! Intelligent insights are working correctly.")
    elif passed >= total * 0.8:
        print("⚠️  Most tests passed. Some minor issues detected.")
    else:
        print("❌ Multiple test failures. Please check the implementation.")

if __name__ == "__main__":
    main()
