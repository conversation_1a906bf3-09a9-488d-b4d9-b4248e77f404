<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Technical & Semantic Analysis</title>

</head>
<body>
    <div class="container">
        <h1>Technical & Semantic Analysis (App-Aware UI Evaluation)</h1>
        <p>This analysis assesses the data flow logic between organizational levels, highlights UI components and their functional roles in app/product filtering, and resolves ambiguities in the dashboard interface.</p>
        
        <h2>Data Flow Logic</h2>
        <div class="flow-diagram">
            <div class="flow-step">
                <div class="flow-number">1</div>
                <div class="flow-content">
                    <div class="flow-title">Strategic (L4 - Management)</div>
                    <div class="flow-description">Cross-application TLS policies prioritized at this level. Security Team sets organization-wide standards that apply to all applications.</div>
                </div>
            </div>
            
            <div class="flow-arrow">↓</div>
            
            <div class="flow-step">
                <div class="flow-number">2</div>
                <div class="flow-content">
                    <div class="flow-title">Tactical (L5 - Team)</div>
                    <div class="flow-description">Product-specific implementations managed by Web Team and Platform Team. Focuses on translating strategic goals into specific product configurations.</div>
                </div>
            </div>
            
            <div class="flow-arrow">↓</div>
            
            <div class="flow-step">
                <div class="flow-number">3</div>
                <div class="flow-content">
                    <div class="flow-title">Implementation (L6/L7)</div>
                    <div class="flow-description">Technical execution details handled by DevOps and App Teams. Focuses on specific remediation steps for individual applications.</div>
                </div>
            </div>
        </div>
        
        <div class="analysis-grid">
            <div class="analysis-card">
                <h3>App-Specific Decision Points</h3>
                <ul class="list-group">
                    <li class="list-group-item">
                        <strong>L4 (Strategic)</strong>
                        Prioritizes cross-app TLS policies affecting all applications regardless of type
                    </li>
                    <li class="list-group-item">
                        <strong>L5 (Tactical)</strong>
                        Web Team focuses on IIS for HR Portal and Payment System, while Platform Team handles Apache for CRM and Inventory
                    </li>
                    <li class="list-group-item">
                        <strong>L6 (Implementation)</strong>
                        DevOps team handles OpenSSL configurations for Payment API, while App Team manages specific web server configurations
                    </li>
                </ul>
            </div>
            
            <div class="analysis-card">
                <h3>Success Rate Discrepancies</h3>
                <ul class="list-group">
                    <li class="list-group-item">
                        <strong>IIS (72%) vs Apache (58%)</strong>
                        IIS shows higher success rates due to more standardized deployment patterns and stronger L5 team ownership
                    </li>
                    <li class="list-group-item">
                        <strong>L5 (49%) vs L6 (53%)</strong>
                        Implementation teams at L6 have more direct control over specific configurations, leading to slightly higher success rates
                    </li>
                    <li class="list-group-item">
                        <strong>"Disable RC4" Success Rate</strong>
                        72% in IIS but only 58% in Tomcat due to differences in configuration complexity and documentation quality
                    </li>
                </ul>
            </div>
        </div>
        
        <h2>UI Components & Functional Roles</h2>
        <div class="ui-components">
            <div class="component-card">
                <div class="component-title">Color-Coded Level Headers</div>
                <div class="component-description">
                    Each organizational level uses distinct color coding to visually separate strategic, tactical, and implementation focuses.
                </div>
                <div class="component-example">
                    <span class="badge badge-primary">L4 - Blue</span> Strategic focus<br>
                    <span class="badge badge-success">L5 - Green</span> Tactical focus<br>
                    <span class="badge badge-warning">L6/L7 - Yellow</span> Implementation focus
                </div>
            </div>
            
            <div class="component-card">
                <div class="component-title">Critical Item Highlighting</div>
                <div class="component-description">
                    Visual indicators draw attention to critical items requiring immediate attention, with color coding based on product type.
                </div>
                <div class="component-example">
                    <span class="badge badge-danger">Red</span> Apache-critical items<br>
                    <span class="badge badge-warning">Yellow</span> Warning indicators<br>
                    <span class="badge badge-success">Green</span> Compliant implementations
                </div>
            </div>
            
            <div class="component-card">
                <div class="component-title">App-Product Relationship Visualization</div>
                <div class="component-description">
                    Connected filtering system shows relationships between applications and their associated products.
                </div>
                <div class="component-example">
                    HR Portal → IIS → Remove Default Welcome Page<br>
                    Payment API → OpenSSL → Upgrade to 3.0.7
                </div>
            </div>
            
            <div class="component-card">
                <div class="component-title">Hierarchical Level Blocks</div>
                <div class="component-description">
                    Funnel visualization shows how recommendations flow from strategic to tactical to implementation levels.
                </div>
                <div class="component-example">
                    L4 (100% width) → L5 (90% width) → L6/L7 (80% width)<br>
                    Connected by downward arrows showing flow
                </div>
            </div>
        </div>
        
        <h2>Resolving Ambiguities</h2>
        <div class="ambiguity-section">
            <div class="ambiguity-title">Identified Ambiguities & Resolutions</div>
            
            <div class="ambiguity-item">
                <div class="ambiguity-question">Why does "Disable RC4" show 72% success in IIS but 58% in Tomcat?</div>
                <div class="ambiguity-resolution">
                    <strong>Resolution:</strong> IIS provides a centralized configuration interface for cipher suites, while Tomcat requires editing multiple configuration files and depends on the underlying JRE/JDK settings. The more complex configuration process for Tomcat results in lower success rates.
                </div>
            </div>
            
            <div class="ambiguity-item">
                <div class="ambiguity-question">Why are there discrepancies in success rate calculations (e.g., 49% in L5 vs. 53% in L6)?</div>
                <div class="ambiguity-resolution">
                    <strong>Resolution:</strong> L6 implementation teams have more direct control over server configurations and specialized expertise with specific products. Additionally, L5 teams manage a broader scope of applications, which can dilute their success rates across more diverse environments.
                </div>
            </div>
            
            <div class="ambiguity-item">
                <div class="ambiguity-question">Why is there missing L6/L7 data for "Nginx HTTP/2 Support"?</div>
                <div class="ambiguity-resolution">
                    <strong>Resolution:</strong> The Nginx HTTP/2 implementation is relatively new, and some applications haven't completed the full implementation cycle through all organizational levels. The feature is still being rolled out in phases, with L4 and L5 planning complete but L6/L7 execution still in progress for some applications.
                </div>
            </div>
        </div>
        
        <h2>Semantic Context for Filtering</h2>
        <p>The dashboard implements vertical filtering (organizational hierarchy) with app/product context to provide a comprehensive view of recommendations:</p>
        
        <ol>
            <li><strong>Vertical Filtering:</strong> Recommendations flow from L4 (strategic) → L5 (tactical) → L6/L7 (implementation) with increasing specificity at each level</li>
            <li><strong>App Context:</strong> Each recommendation is linked to specific applications (e.g., HR Portal, Payment API) to provide implementation context</li>
            <li><strong>Product Context:</strong> Products (e.g., IIS, Apache, OpenSSL) are associated with recommendations to clarify technical requirements</li>
            <li><strong>Cross-Level Relationships:</strong> The dashboard visualizes how high-level strategic decisions (e.g., "Disable TLS 1.0") translate to specific implementation tasks (e.g., "Update OpenSSL configuration")</li>
        </ol>
        
        <h2>Actionable UI Improvements</h2>
        <ol>
            <li><strong>Add app-filter dropdowns</strong> for cross-level analysis to allow filtering by specific applications</li>
            <li><strong>Implement product-specific views</strong> to focus on particular technologies (IIS, Apache, etc.)</li>
            <li><strong>Standardize success rate calculations</strong> across levels to improve consistency</li>
            <li><strong>Add tooltips for ambiguous metrics</strong> to explain calculation methodologies</li>
            <li><strong>Enhance visualization of app-to-product relationships</strong> with interactive diagrams</li>
        </ol>
    </div>
</body>
</html>