/* Cluster Controls Container */
.cluster-controls {
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 100;
    display: flex;
    flex-direction: column;
    gap: 10px;
    padding: 5px;
    border-radius: 5px;
    background-color: rgba(255, 255, 255, 0.85);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    pointer-events: auto;
}

/* Control Button Groups */
.control-group {
    background-color: #0076CE; /* Strong blue background */
    border-radius: 4px;
    padding: 3px;
    display: flex;
    flex-direction: column;
    gap: 3px;
}

/* Buttons with solid colors and strong icons */
.control-btn {
    width: 36px;
    height: 36px;
    border: none;
    background-color: #0076CE;
    color: white;
    font-size: 1rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    transition: background-color 0.3s ease, transform 0.3s ease;
}

.control-btn:hover {
    background-color: #0057A3;
}

.control-btn:active {
    transform: scale(0.95);
}

.control-btn i {
    color: inherit;
}

/* Ensures zoom group listens to pointer events */
.cluster-svg, .cluster-svg * {
    pointer-events: all;
}

.cluster-visualization-container .zoom-group {
    transition: transform 0.2s;
}

/* Beautify the graph SVG */
.cluster-svg {
    background-color: #ffffff;
    border: 1px solid #ccc;
    border-radius: 4px;
    min-height: 300px;
}

/* Spinner styling */
.cluster-visualization-container .loading-spinner {
    border: 4px solid rgba(0, 118, 206, 0.1);
    border-top: 4px solid #0076CE;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    animation: spin 1s linear infinite;
    margin: 20px auto;
}
@keyframes spin {
    0% { transform: rotate(0); }
    100% { transform: rotate(360deg); }
}

#clusterVisualizationContainer.fullscreen-viz {
    position: fixed; top: 10px; left: 10px; right: 10px; bottom: 10px;
    width: calc(100vw - 20px); height: calc(100vh - 20px);
    background-color: #f9f9f9; z-index: 1000;
    border: 1px solid #aaa; box-shadow: 0 0 15px rgba(0,0,0,0.3);
    transition: all 0.3s ease-in-out;
  }
  #clusterVisualizationContainer.fullscreen-viz .cluster-svg {
     width: 100%; height: 100%;
  }
  #clusterVisualizationContainer .cluster-controls {
     position: absolute; top: 10px; right: 10px; z-index: 1001;
  }
  #fullscreen-toggle-btn {
    position: absolute; bottom: 15px; right: 15px; z-index: 1001;
    background-color: rgba(255, 255, 255, 0.8); border: 1px solid #ccc;
    border-radius: 4px; padding: 5px 8px; cursor: pointer;
    font-size: 14px; line-height: 1; box-shadow: 0 1px 3px rgba(0,0,0,0.2);
  }
  #fullscreen-toggle-btn:hover { background-color: #f0f0f0; }#clusterVisualizationContainer.fullscreen-viz {
    position: fixed; top: 10px; left: 10px; right: 10px; bottom: 10px;
    width: calc(100vw - 20px); height: calc(100vh - 20px);
    background-color: #f9f9f9; z-index: 1000;
    border: 1px solid #aaa; box-shadow: 0 0 15px rgba(0,0,0,0.3);
    transition: all 0.3s ease-in-out;
  }
  #clusterVisualizationContainer.fullscreen-viz .cluster-svg {
     width: 100%; height: 100%;
  }
  #clusterVisualizationContainer .cluster-controls {
     position: absolute; top: 10px; right: 10px; z-index: 1001;
  }
  #fullscreen-toggle-btn {
    position: absolute; bottom: 15px; right: 15px; z-index: 1001;
    background-color: rgba(255, 255, 255, 0.8); border: 1px solid #ccc;
    border-radius: 4px; padding: 5px 8px; cursor: pointer;
    font-size: 14px; line-height: 1; box-shadow: 0 1px 3px rgba(0,0,0,0.2);
  }
  #fullscreen-toggle-btn:hover { background-color: #f0f0f0; }