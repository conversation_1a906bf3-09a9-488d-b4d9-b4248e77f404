<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Interactive Map Panel</title>
    <link rel="stylesheet" href="assets/map-panel.css">
    <!-- Load Leaflet CSS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css"
          integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY="
          crossorigin=""/>
    <!-- Load Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <!-- Load Dell-branded styles -->
    <link rel="stylesheet" href="assets/styles.css">
</head>
<body>
    <header class="navbar">
        <div class="container">
            <div class="navbar-brand">Interactive Map Panel</div>
            <div class="navbar-controls">
                <button id="fullscreen-btn" class="dell-button" title="Toggle Fullscreen">
                    <i class="fa-solid fa-expand"></i>
                </button>
            </div>
        </div>
    </header>

    <main class="container">
        <div class="map-container">
            <!-- The main map will be inserted here by JavaScript -->
            <div id="map"></div>
            
            <!-- Minimap overlay -->
            <div id="minimap" class="minimap"></div>
            
            <!-- Map controls -->
            <div class="map-controls">
                <!-- Zoom controls -->
                <div class="control-group">
                    <button id="zoom-in" class="control-btn" title="Zoom In">
                        <i class="fa-solid fa-plus"></i>
                    </button>
                    <button id="zoom-out" class="control-btn" title="Zoom Out">
                        <i class="fa-solid fa-minus"></i>
                    </button>
                </div>
                
                <!-- Layer controls -->
                <div class="control-group">
                    <button id="layer-toggle" class="control-btn" title="Toggle Layers">
                        <i class="fa-solid fa-layer-group"></i>
                    </button>
                </div>
                
                <!-- View options -->
                <div class="control-group">
                    <button id="view-toggle" class="control-btn" title="Change View">
                        <i class="fa-solid fa-map"></i>
                    </button>
                </div>

                <!-- Bookmark controls -->
                <div class="control-group">
                    <button id="bookmark-toggle" class="control-btn" title="Bookmarks">
                        <i class="fa-solid fa-bookmark"></i>
                    </button>
                </div>
                
                <!-- Reset view -->
                <div class="control-group">
                    <button id="reset-view" class="control-btn" title="Reset View">
                        <i class="fa-solid fa-house"></i>
                    </button>
                </div>
            </div>
            
            <!-- Scale indicator -->
            <div id="scale-indicator" class="scale-indicator"></div>
            
            <!-- Cardinal direction marker -->
            <div class="cardinal-marker">
                <div class="cardinal-north">N</div>
                <div class="cardinal-direction-pointer"></div>
            </div>
            
            <!-- Layer panel (hidden by default) -->
            <div id="layer-panel" class="panel layer-panel">
                <div class="panel-header">
                    <h3>Map Layers</h3>
                    <button class="close-btn">×</button>
                </div>
                <div class="panel-content">
                    <div class="layer-option">
                        <input type="checkbox" id="layer-roads" checked>
                        <label for="layer-roads">Roads</label>
                    </div>
                    <div class="layer-option">
                        <input type="checkbox" id="layer-poi" checked>
                        <label for="layer-poi">Points of Interest</label>
                    </div>
                    <div class="layer-option">
                        <input type="checkbox" id="layer-buildings">
                        <label for="layer-buildings">Buildings</label>
                    </div>
                    <div class="layer-option">
                        <input type="checkbox" id="layer-terrain">
                        <label for="layer-terrain">Terrain</label>
                    </div>
                    <div class="layer-option">
                        <input type="checkbox" id="layer-transit">
                        <label for="layer-transit">Public Transit</label>
                    </div>
                    <div class="layer-option">
                        <input type="checkbox" id="layer-data">
                        <label for="layer-data">Data Overlay</label>
                    </div>
                </div>
            </div>
            
            <!-- View options panel (hidden by default) -->
            <div id="view-panel" class="panel view-panel">
                <div class="panel-header">
                    <h3>Map Style</h3>
                    <button class="close-btn">×</button>
                </div>
                <div class="panel-content">
                    <div class="view-option">
                        <input type="radio" name="map-style" id="style-standard" checked>
                        <label for="style-standard">Standard</label>
                    </div>
                    <div class="view-option">
                        <input type="radio" name="map-style" id="style-satellite">
                        <label for="style-satellite">Satellite</label>
                    </div>
                    <div class="view-option">
                        <input type="radio" name="map-style" id="style-terrain">
                        <label for="style-terrain">Terrain</label>
                    </div>
                    <div class="view-option">
                        <input type="radio" name="map-style" id="style-night">
                        <label for="style-night">Night Mode</label>
                    </div>
                    <div class="view-option">
                        <input type="radio" name="map-style" id="style-minimal">
                        <label for="style-minimal">Minimal</label>
                    </div>
                </div>
            </div>
            
            <!-- Bookmarks panel (hidden by default) -->
            <div id="bookmark-panel" class="panel bookmark-panel">
                <div class="panel-header">
                    <h3>Location Bookmarks</h3>
                    <button class="close-btn">×</button>
                </div>
                <div class="panel-content">
                    <div class="bookmark-list">
                        <!-- Default bookmarks -->
                        <div class="bookmark-item" data-lat="40.7128" data-lng="-74.0060" data-zoom="12">
                            <span class="bookmark-name">New York City</span>
                            <button class="bookmark-goto"><i class="fa-solid fa-arrow-right"></i></button>
                        </div>
                        <div class="bookmark-item" data-lat="34.0522" data-lng="-118.2437" data-zoom="12">
                            <span class="bookmark-name">Los Angeles</span>
                            <button class="bookmark-goto"><i class="fa-solid fa-arrow-right"></i></button>
                        </div>
                        <div class="bookmark-item" data-lat="41.8781" data-lng="-87.6298" data-zoom="12">
                            <span class="bookmark-name">Chicago</span>
                            <button class="bookmark-goto"><i class="fa-solid fa-arrow-right"></i></button>
                        </div>
                    </div>
                    <div class="bookmark-actions">
                        <button id="add-bookmark" class="dell-button">
                            <i class="fa-solid fa-plus"></i> Add Current View
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- Add Bookmark Dialog (hidden by default) -->
            <div id="add-bookmark-dialog" class="dialog">
                <div class="dialog-content">
                    <div class="dialog-header">
                        <h3>Add Bookmark</h3>
                        <button class="close-btn">×</button>
                    </div>
                    <div class="dialog-body">
                        <div class="form-group">
                            <label for="bookmark-name">Bookmark Name</label>
                            <input type="text" id="bookmark-name" placeholder="Enter a name for this location">
                        </div>
                        <div class="form-group">
                            <label for="bookmark-notes">Notes (optional)</label>
                            <textarea id="bookmark-notes" placeholder="Add notes about this location"></textarea>
                        </div>
                    </div>
                    <div class="dialog-footer">
                        <button id="cancel-bookmark" class="dell-button secondary">Cancel</button>
                        <button id="save-bookmark" class="dell-button">Save</button>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Load Leaflet JavaScript -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"
            integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo="
            crossorigin=""></script>
    <!-- Load our Map Panel JavaScript -->
    <script src="assets/map-panel.js"></script>
</body>
</html>