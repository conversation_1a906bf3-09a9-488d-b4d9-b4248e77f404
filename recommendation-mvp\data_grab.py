import pandas as pd
from sklearn.cluster import KMeans
from sklearn.preprocessing import LabelEncoder
import pymssql
import hvac
from urllib3.exceptions import InsecureRequestWarning
from urllib3 import disable_warnings
import logging

# Disable SSL warnings
disable_warnings(InsecureRequestWarning)

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# Fetch credentials from Vault
def get_db_credentials():
    VAULT_ADDR = 'https://vault.dell.com/'
    client = hvac.Client(url=VAULT_ADDR, namespace='1007693', verify=False)
    client.auth.approle.login(
        role_id='9fa55d6a-7e05-e025-a2e8-349b6ee46c4a',
        secret_id='9c6369d4-6656-df2c-b5e6-919c1d593340'
    )
    secret = client.secrets.kv.v2.read_secret_version('appcred', mount_point='kv')
    return secret['data']['data']

# Fetch database credentials
credentials = get_db_credentials()
db_user = credentials['prod.datasource.username']
db_password = credentials['prod.datasource.password']

# Fetch data from the local database
def fetch_data(db_user, db_password, db_server='vsrpwsqp1pr12.amer.dell.com', db_name='VSDI'):
    df = None
    df_dash = None
    
    try:
        logging.info("Connecting to database...")
        with pymssql.connect(server=db_server, user=db_user, password=db_password, database=db_name) as conn:
            cursor = conn.cursor(as_dict=True)
            
            # Fetch data from VSR_AUDIT_SERVICENOW_REQUESTS_V2
            logging.info("Fetching data from VSR_AUDIT_SERVICENOW_REQUESTS_V2...")
            cursor.execute("SELECT details, * FROM VSR_AUDIT_SERVICENOW_REQUESTS_V2")
            data1 = cursor.fetchall()
            df1 = pd.DataFrame(data1)
            logging.info(f"Fetched {len(df1)} rows from VSR_AUDIT_SERVICENOW_REQUESTS_V2")
            
            # Fetch data from VSR_COLLECTION_INVENTORY_V2
            logging.info("Fetching data from VSR_COLLECTION_INVENTORY_V2...")
            cursor.execute("SELECT NAME, APP_NAME, APPOWNER, EMAIL FROM VSR_COLLECTION_INVENTORY_V2")
            data2 = cursor.fetchall()
            df2 = pd.DataFrame(data2)
            logging.info(f"Fetched {len(df2)} rows from VSR_COLLECTION_INVENTORY_V2")
            
            # Join the two dataframes
            logging.info("Joining dataframes...")
            df = pd.merge(df1, df2, left_on='KENNA_ASSET_HOSTNAME', right_on='NAME', how='inner')
            
            logging.info(f"Joined dataframe has {len(df)} rows")
            logging.info(f"Columns in joined dataframe: {df.columns.tolist()}")
            logging.info(f"Non-null counts for new columns: \n{df[['APP_NAME', 'APPOWNER', 'EMAIL']].notnull().sum()}")
            
            if df[['APP_NAME', 'APPOWNER', 'EMAIL']].isna().all().any():
                logging.warning("One or more of the new columns (APP_NAME, APPOWNER, EMAIL) are entirely empty")
            
            # Directly query the L-columns we need using the schema provided by the user
            try:
                logging.info("Fetching L-columns from VSDI_DASH_MAIN_ALL...")
                
                # These are the specific L-columns identified in the schema
                l_columns_query = """
                SELECT TOP 5000
                    CMDB_L2,
                    L2_Name,
                    CMDB_L3_NAME,
                    CMDB_L4_NAME,
                    CMDB_L5_NAME,
                    CMDB_L6_NAME,
                    CMDB_L7_NAME,
                    CMDB_L8_NAME
                FROM VSDI_DASH_MAIN_ALL
                """
                
                logging.info("Executing direct L-columns query with limit")
                
                # Execute with timeout
                try:
                    conn.timeout = 30  # 30 seconds timeout
                    cursor.execute(l_columns_query)
                    data_dash = cursor.fetchall()
                    if data_dash:
                        df_dash = pd.DataFrame(data_dash)
                        logging.info(f"Fetched {len(df_dash)} rows from VSDI_DASH_MAIN_ALL with specific L-columns")
                        # Explicitly list the L-columns we're using
                        l_columns = ['CMDB_L2', 'L2_Name', 'CMDB_L3_NAME', 'CMDB_L4_NAME', 
                                     'CMDB_L5_NAME', 'CMDB_L6_NAME', 'CMDB_L7_NAME', 'CMDB_L8_NAME']
                        logging.info(f"Using L-columns: {l_columns}")
                    else:
                        logging.warning("No data returned from VSDI_DASH_MAIN_ALL")
                        df_dash = None
                except Exception as query_ex:
                    logging.error(f"Error executing VSDI_DASH_MAIN_ALL L-columns query: {query_ex}")
                    # Try a smaller sample as fallback
                    try:
                        logging.info("Trying with a smaller sample...")
                        l_columns_query = l_columns_query.replace("TOP 5000", "TOP 1000")
                        cursor.execute(l_columns_query)
                        data_dash = cursor.fetchall()
                        if data_dash:
                            df_dash = pd.DataFrame(data_dash)
                            logging.info(f"Fetched {len(df_dash)} rows from VSDI_DASH_MAIN_ALL (reduced sample)")
                            # Explicitly list the L-columns we're using
                            l_columns = ['CMDB_L2', 'L2_Name', 'CMDB_L3_NAME', 'CMDB_L4_NAME', 
                                         'CMDB_L5_NAME', 'CMDB_L6_NAME', 'CMDB_L7_NAME', 'CMDB_L8_NAME']
                        else:
                            logging.warning("No data returned from reduced sample query")
                            df_dash = None
                    except Exception as ex:
                        logging.error(f"Failed to fetch even with reduced sample: {ex}")
                        df_dash = None
            except Exception as e:
                logging.error(f"Error fetching VSDI_DASH_MAIN_ALL: {e}")
                logging.warning("Continuing without VSDI_DASH_MAIN_ALL data")
    
    except Exception as e:
        logging.error(f"Error in database connection or data fetching: {e}")
    
    return df, df_dash

df, df_dash = fetch_data(db_user, db_password)

# Check if the data was fetched successfully
if df is None or df.empty:
    raise ValueError("Failed to fetch data or data is empty.")

# Data Preprocessing
if df is not None:
    # Ensure the EXECUTION column is formatted correctly
    df['EXECUTION'] = df['EXECUTION'].str.strip().str.lower()
    
    # Calculate YES_COUNT
    df['YES_COUNT'] = df.groupby('KENNA_ASSET_HOSTNAME')['EXECUTION'].transform(lambda x: (x == 'yes').sum())
    
    # Encode Hostnames and Vulnerabilities
    hostname_encoder = LabelEncoder()
    df['HOSTNAME_CODE'] = hostname_encoder.fit_transform(df['KENNA_ASSET_HOSTNAME'])
    
    vulnerability_encoder = LabelEncoder()
    df['VULNERABILITY_CODE'] = vulnerability_encoder.fit_transform(df['RELATED_VULNERABILITY'])
    
    # Handle NaN values
    df.fillna('', inplace=True)
    
    logging.info("Data preprocessing completed successfully")
    
    # Create a mapping of paths
    df['PATH_USAGE'] = df.apply(lambda x: f"Path {x['details']} - frequently" if x['EXECUTION'] == 'yes' else f"Path {x['details']} - Not frequently", axis=1)
    
    # Group and save path usage summary
    path_summary = df.groupby('APP_NAME')['PATH_USAGE'].apply(lambda x: "\n".join(x)).reset_index()
    path_summary.to_csv('/home/<USER>/path_summary.csv', index=False)
    logging.info("Path summary saved successfully")

# Process VSDI_DASH_MAIN_ALL table to extract L-levels - optimized version
try:
    if df_dash is not None:
        # We already know the L-columns from the query
        l_columns = ['CMDB_L2', 'L2_Name', 'CMDB_L3_NAME', 'CMDB_L4_NAME', 
                     'CMDB_L5_NAME', 'CMDB_L6_NAME', 'CMDB_L7_NAME', 'CMDB_L8_NAME']
        logging.info(f"Using defined L-columns for processing: {l_columns}")
        
        # More efficient mapping approach
        l_mapping = {}
        
        # Process in batches to reduce memory pressure
        batch_size = 1000
        total_rows = len(df_dash)
        
        for start_idx in range(0, total_rows, batch_size):
            end_idx = min(start_idx + batch_size, total_rows)
            logging.info(f"Processing L-columns batch {start_idx}-{end_idx} of {total_rows}")
            
            for _, row in df_dash.iloc[start_idx:end_idx].iterrows():
                # Get non-empty L values using list comprehension (more efficient)
                # Only include columns that exist in the dataframe and have non-empty values
                row_l_values = [str(row[col]).strip() for col in l_columns 
                               if col in df_dash.columns and col in row and 
                               row[col] and str(row[col]).strip()]
                
                if row_l_values:
                    # Use a set for the row values (faster operations)
                    row_l_set = set(row_l_values)
                    for l_value in row_l_values:
                        if l_value in l_mapping:
                            l_mapping[l_value].update(row_l_set)
                        else:
                            l_mapping[l_value] = row_l_set
        
        logging.info(f"Created mapping for {len(l_mapping)} L-values")
        
        # Convert sets to sorted lists for consistent output
        for key in l_mapping:
            l_mapping[key] = sorted(l_mapping[key])
        
        # Create mappings from APPOWNER to individual L-columns
        unique_appowners = df['APPOWNER'].dropna().unique()
        
        # First create a combined mapping for the APP_LEVELS column (all L values comma-separated)
        appowner_to_levels = {}
        
        # Then create individual mappings for each L column
        appowner_to_l2 = {}
        appowner_to_l2_name = {}
        appowner_to_l3 = {}
        appowner_to_l4 = {}
        appowner_to_l5 = {}
        appowner_to_l6 = {}
        appowner_to_l7 = {}
        appowner_to_l8 = {}
        
        # For each row in the L columns data
        for _, row in df_dash.iterrows():
            # Process only rows that have some data
            if any(str(row.get(col, '')).strip() for col in l_columns if col in df_dash.columns):
                # First, collect all L values for this row that aren't empty
                l_values = []
                
                # Get values for each L column
                l2_val = str(row.get('CMDB_L2', '')).strip()
                l2_name_val = str(row.get('L2_Name', '')).strip()
                l3_val = str(row.get('CMDB_L3_NAME', '')).strip()
                l4_val = str(row.get('CMDB_L4_NAME', '')).strip()
                l5_val = str(row.get('CMDB_L5_NAME', '')).strip()
                l6_val = str(row.get('CMDB_L6_NAME', '')).strip()
                l7_val = str(row.get('CMDB_L7_NAME', '')).strip()
                l8_val = str(row.get('CMDB_L8_NAME', '')).strip()
                
                # Add non-empty values to our list
                for val in [l2_val, l2_name_val, l3_val, l4_val, l5_val, l6_val, l7_val, l8_val]:
                    if val:
                        l_values.append(val)
                
                # For each valid L value, update the mappings
                for val in l_values:
                    if val:
                        # Add mappings for this L value to each specific L column
                        if l2_val and val != l2_val:  # Don't map to itself
                            if val not in appowner_to_l2:
                                appowner_to_l2[val] = l2_val
                                
                        if l2_name_val and val != l2_name_val:
                            if val not in appowner_to_l2_name:
                                appowner_to_l2_name[val] = l2_name_val
                                
                        if l3_val and val != l3_val:
                            if val not in appowner_to_l3:
                                appowner_to_l3[val] = l3_val
                                
                        if l4_val and val != l4_val:
                            if val not in appowner_to_l4:
                                appowner_to_l4[val] = l4_val
                                
                        if l5_val and val != l5_val:
                            if val not in appowner_to_l5:
                                appowner_to_l5[val] = l5_val
                                
                        if l6_val and val != l6_val:
                            if val not in appowner_to_l6:
                                appowner_to_l6[val] = l6_val
                                
                        if l7_val and val != l7_val:
                            if val not in appowner_to_l7:
                                appowner_to_l7[val] = l7_val
                                
                        if l8_val and val != l8_val:
                            if val not in appowner_to_l8:
                                appowner_to_l8[val] = l8_val
        
        # Now use the l_mapping for the combined APP_LEVELS column
        for appowner in unique_appowners:
            appowner_clean = str(appowner).strip()
            if appowner_clean in l_mapping:
                appowner_to_levels[appowner_clean] = ", ".join(l_mapping[appowner_clean])
            else:
                appowner_to_levels[appowner_clean] = appowner_clean
        
        # Add the APP_LEVELS column (combined values)
        df['APP_LEVELS'] = df['APPOWNER'].apply(
            lambda x: appowner_to_levels.get(str(x).strip(), '') if x else ''
        )
        
        # Add individual L columns to the main dataframe
        df['CMDB_L2'] = df['APPOWNER'].apply(
            lambda x: appowner_to_l2.get(str(x).strip(), '') if x else ''
        )
        
        df['L2_Name'] = df['APPOWNER'].apply(
            lambda x: appowner_to_l2_name.get(str(x).strip(), '') if x else ''
        )
        
        df['CMDB_L3_NAME'] = df['APPOWNER'].apply(
            lambda x: appowner_to_l3.get(str(x).strip(), '') if x else ''
        )
        
        df['CMDB_L4_NAME'] = df['APPOWNER'].apply(
            lambda x: appowner_to_l4.get(str(x).strip(), '') if x else ''
        )
        
        df['CMDB_L5_NAME'] = df['APPOWNER'].apply(
            lambda x: appowner_to_l5.get(str(x).strip(), '') if x else ''
        )
        
        df['CMDB_L6_NAME'] = df['APPOWNER'].apply(
            lambda x: appowner_to_l6.get(str(x).strip(), '') if x else ''
        )
        
        df['CMDB_L7_NAME'] = df['APPOWNER'].apply(
            lambda x: appowner_to_l7.get(str(x).strip(), '') if x else ''
        )
        
        df['CMDB_L8_NAME'] = df['APPOWNER'].apply(
            lambda x: appowner_to_l8.get(str(x).strip(), '') if x else ''
        )
        
        logging.info("Added APP_LEVELS and individual L columns to the main dataframe")
except Exception as e:
    logging.error(f"Error processing L-levels: {e}")
    # Ensure we can continue even if L-levels processing fails
    if 'APP_LEVELS' not in df.columns:
        df['APP_LEVELS'] = ''

# Apply KMeans clustering
if df is not None:
    kmeans = KMeans(n_clusters=3, random_state=42)
    df['CLUSTER'] = kmeans.fit_predict(df[['YES_COUNT', 'VULNERABILITY_CODE', 'HOSTNAME_CODE']].values)
    logging.info("KMeans clustering applied successfully")

# Make sure APP_LEVELS column exists before saving
if 'APP_LEVELS' not in df.columns:
    df['APP_LEVELS'] = ''
    logging.warning("APP_LEVELS column was missing, added empty column")

# Save the original and clustered data
if df is not None:
    # Ensure the output directory exists
    import os
    os.makedirs('/home/<USER>', exist_ok=True)
    
    data_path = '/home/<USER>/data.csv'
    clustered_data_path = '/home/<USER>/clustered_data.csv'
    clustered_data_L_path = '/home/<USER>/clustered_data_L.csv'
    
    # Log the column names for debugging
    logging.info(f"Final columns in dataframe: {df.columns.tolist()}")
    
    # Save data.csv
    try:
        df.to_csv(data_path, index=False)
        logging.info(f"Data saved successfully to {data_path}")
    except Exception as e:
        logging.error(f"Error saving data: {e}")
    
    # Save clustered_data.csv
    try:
        df.to_csv(clustered_data_path, index=False)
        logging.info(f"Clustered data saved successfully to {clustered_data_path}")
    except Exception as e:
        logging.error(f"Error saving clustered data: {e}")
    
    # Always save clustered_data_L.csv (this is the one we want to make sure gets created)
    try:
        df.to_csv(clustered_data_L_path, index=False)
        # Double-check the file was created
        if os.path.exists(clustered_data_L_path):
            logging.info(f"Clustered data with L-levels saved successfully to {clustered_data_L_path}")
            logging.info(f"File size: {os.path.getsize(clustered_data_L_path)} bytes")
        else:
            logging.error(f"Failed to verify {clustered_data_L_path} was created")
    except Exception as e:
        logging.error(f"Error saving clustered data with L-levels: {e}")
        # Try alternate location if home directory is not accessible
        try:
            alt_path = './clustered_data_L.csv'
            df.to_csv(alt_path, index=False)
            logging.info(f"Clustered data with L-levels saved to alternate location: {alt_path}")
        except Exception as alt_e:
            logging.error(f"Failed to save to alternate location: {alt_e}")

# Print the first few rows of the dataframe to verify the new columns
if df is not None:
    print("Sample of dataframe with L-columns:")
    print(df[['KENNA_ASSET_HOSTNAME', 'APP_NAME', 'APPOWNER', 'EMAIL', 'PATH_USAGE', 
             'APP_LEVELS', 'CMDB_L2', 'L2_Name', 'CMDB_L3_NAME', 'CMDB_L4_NAME', 
             'CMDB_L5_NAME', 'CMDB_L6_NAME', 'CMDB_L7_NAME', 'CMDB_L8_NAME']].head())
