import os

class Config:
    # Basic Flask configuration
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'dev-key-123'
    
    # Application specific settings
    ITEMS_PER_PAGE = 10
    DATA_FILE = 'clustered_data.csv'
    RECOMMENDATION_OUTPUT = 'recommend_data.json'
    
    # Logging configuration
    LOG_LEVEL = 'INFO'
    LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    
    # Development specific settings
    DEBUG = True
