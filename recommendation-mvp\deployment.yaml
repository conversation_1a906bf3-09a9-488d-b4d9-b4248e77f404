apiVersion: apps/v1
kind: Deployment
metadata:
  name: vsr-recommendation-tool-deployment
  namespace: vsrnp
spec:
  selector:
    matchLabels:
      app: vsr-recommendation-tool
  replicas: 2
  template:
    metadata:
      labels:
        app: vsr-recommendation-tool
    spec:
      containers:
        - name: vsr-recommendation-tool
          image: harbor.dell.com/vsr-image-repo/vsr-recommendation-tool:_BUILD_VERSION
          ports:
            - containerPort: 8080
              name: http
              protocol: TCP
              readinessProbe:
                httpGet:
                  path: /
                  port: 80
                initialDelaySeconds: 20
                periodSeconds: 7
                failureThreshold: 3
                successThreshold: 1