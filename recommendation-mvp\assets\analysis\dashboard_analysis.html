<div class="container"> <header>
    <h1>IT Operations Dashboard Analysis</h1>
    <div class="subtitle">Comprehensive analysis of the web-based IT operations dashboard interface</div>
</header>

<div class="key-findings">
    <h2>Key Findings</h2>
    <div class="findings-grid">
        <div class="finding-item">
            <div class="finding-title">Organizational Hierarchy</div>
            <div class="finding-description">Clear vertical filtering from L4 (Management) → L5 (Team) → L6/L7 (Implementation) with mapped personnel and focus areas.</div>
        </div>
        <div class="finding-item">
            <div class="finding-title">Product Success Rates</div>
            <div class="finding-description">TLS/SSL (72%), IIS (65%), Apache (58%) show varying success rates based on implementation complexity and team expertise.</div>
        </div>
        <div class="finding-item">
            <div class="finding-title">App-Product Integration</div>
            <div class="finding-description">Strong correlation between applications and their associated products, with clear ownership mapping across organizational levels.</div>
        </div>
        <div class="finding-item">
            <div class="finding-title">Data Inconsistencies</div>
            <div class="finding-description">Success rate discrepancies between levels (49% in L5 vs 53% in L6) and missing L6/L7 data for some implementations.</div>
        </div>
    </div>
</div>

<h2>Detailed Analysis Sections</h2>
<div class="section-grid">
    <div class="section-card">
        <div class="section-title">Organizational Recommendation Funnel</div>
        <div class="section-description">
            Hierarchy breakdown (L4-Management, L5-Team, L6/L7-Implementation) with mapped personnel, background colors, and strategic vs. tactical focus descriptions. Includes specific product recommendations with execution counts and success rates.
        </div>
        <a href="org_recommendation_funnel.html" class="section-link">View Analysis</a>
    </div>

    <div class="section-card">
        <div class="section-title">Product Rankings & Recommendations</div>
        <div class="section-description">
            Top-ranked products with execution metrics, success rates, and associated apps. Includes cross-product recommendations section with app dependencies and success rate discrepancies analysis.
        </div>
         <a href="product_rankings.html" class="section-link">View Analysis</a>
    </div>

    <div class="section-card">
        <div class="section-title">App & Product Data Integration</div>
        <div class="section-description">
            Correlation of product details with applications, verification of hierarchical relationships between product recommendations, organizational levels, and app ownership. Includes flagged inconsistencies and app-to-product mapping gaps.
        </div>
         <a href="app_product_integration.html" class="section-link">View Analysis</a>
    </div>

    <div class="section-card">
        <div class="section-title">Technical & Semantic Analysis</div>
        <div class="section-description">
            Assessment of data flow logic between organizational levels, UI components and their functional roles in app/product filtering, and resolution of ambiguities in the dashboard interface.
        </div>
         <a href="technical_analysis.html" class="section-link">View Analysis</a>
    </div>
</div>

<h2>Structured Output Summary</h2>
<table class="summary-table">
    <thead>
        <tr>
            <th>Level</th>
            <th>Product</th>
            <th>App</th>
            <th>Success Rate</th>
            <th>Owner</th>
            <th>Remediation Steps</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>L4</td>
            <td>TLS Standards</td>
            <td>All</td>
            <td>85%</td>
            <td>Security Team</td>
            <td>Disable TLS 1.0/1.1</td>
        </tr>
        <tr>
            <td>L5</td>
            <td>IIS</td>
            <td>HR App</td>
            <td>72%</td>
            <td>Web Team</td>
            <td>Patch CVE-2023-1234</td>
        </tr>
        <tr>
            <td>L6</td>
            <td>OpenSSL</td>
            <td>Payment API</td>
            <td>53%</td>
            <td>DevOps</td>
            <td>Upgrade to 3.0.7</td>
        </tr>
        <tr>
            <td>L5</td>
            <td>Apache</td>
            <td>CRM</td>
            <td>58%</td>
            <td>Platform Team</td>
            <td>Install ModSecurity</td>
        </tr>
        <tr>
            <td>L6</td>
            <td>Nginx</td>
            <td>API Gateway</td>
            <td>67%</td>
            <td>Platform Team</td>
            <td>Enable HTTP/2</td>
        </tr>
    </tbody>
</table>

<h2>Actionable Insights</h2>
<ol>
    <li><strong>Merge duplicate Apache entries</strong> in L5 level to improve consistency</li>
    <li><strong>Add app-filter dropdowns</strong> for cross-level analysis</li>
    <li><strong>Standardize success rate calculations</strong> across levels</li>
    <li><strong>Investigate missing L6/L7 data</strong> for Nginx HTTP/2 support</li>
    <li><strong>Enhance app-to-product mapping</strong> to eliminate gaps</li>
    <li><strong>Implement product-specific views</strong> to focus on particular technologies</li>
    <li><strong>Add tooltips for ambiguous metrics</strong> to explain calculation methodologies</li>
</ol>

</div>
