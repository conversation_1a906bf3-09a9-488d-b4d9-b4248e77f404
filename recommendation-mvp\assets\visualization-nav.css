/* Visualization Navigation CSS */

/* Tooltip */
.visualization-tooltip {
    position: absolute;
    padding: 8px 12px;
    background-color: rgba(0, 0, 0, 0.8);
    color: white;
    border-radius: 4px;
    font-size: 12px;
    z-index: 1000;
    pointer-events: none;
    max-width: 250px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    line-height: 1.4;
}

/* Breadcrumb navigation */
.breadcrumb-navigation {
    display: flex;
    padding: 10px;
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    margin-bottom: 15px;
    align-items: center;
    font-size: 14px;
}

.breadcrumb-navigation a {
    color: #0076CE;
    text-decoration: none;
}

.breadcrumb-navigation a:hover {
    text-decoration: underline;
}

.breadcrumb-navigation span {
    margin: 0 8px;
    color: #6c757d;
}

/* Navigation Controls */
.navigation-controls {
    position: absolute;
    top: 10px;
    right: 10px;
    background-color: rgba(255, 255, 255, 0.9);
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 10px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    z-index: 1000;
    display: flex;
    flex-direction: column;
    gap: 10px;
    max-width: 230px;
    user-select: none;
    transition: opacity 0.3s ease;
}

.nav-section-title {
    font-weight: bold;
    color: #0076CE;
    margin-bottom: 6px;
    font-size: 14px;
    text-align: center;
    border-bottom: 1px solid #eee;
    padding-bottom: 4px;
}

.nav-section {
    display: flex;
    flex-direction: column;
    gap: 5px;
    margin-bottom: 5px;
    padding-bottom: 5px;
    border-bottom: 1px solid #f0f0f0;
}

.nav-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.nav-section-label {
    font-size: 12px;
    color: #6c757d;
    margin-bottom: 2px;
}

.nav-button-group {
    display: flex;
    gap: 5px;
    justify-content: center;
}

.pan-controls {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-template-rows: repeat(3, 1fr);
    gap: 2px;
    margin: 5px 0;
    justify-items: center;
}

.nav-button {
    cursor: pointer;
    background-color: #f8f9fa;
    border: 1px solid #ced4da;
    border-radius: 4px;
    padding: 5px;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s;
    min-width: 30px;
    min-height: 30px;
}

.nav-button:hover {
    background-color: #e9ecef;
    border-color: #0076CE;
    color: #0076CE;
}

.nav-button:active {
    background-color: #dee2e6;
    transform: scale(0.95);
}

.nav-button[disabled] {
    opacity: 0.5;
    cursor: not-allowed;
    border-color: #ced4da;
}

.reset-button {
    width: 100%;
    padding: 6px;
    background-color: #f1f8ff;
    border-color: #c8e1ff;
    color: #0366d6;
    font-weight: medium;
}

.reset-button:hover {
    background-color: #dbedff;
    border-color: #0366d6;
}

/* Keyboard shortcuts dialog */
.shortcuts-dialog {
    z-index: 2000;
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.3);
}

.shortcuts-dialog table {
    border-collapse: collapse;
    width: 100%;
}

.shortcuts-dialog th,
.shortcuts-dialog td {
    text-align: left;
    padding: 8px 12px;
}

.shortcuts-dialog th {
    background-color: #f8f9fa;
    border-bottom: 2px solid #ddd;
}

.shortcuts-dialog tr:nth-child(even) {
    background-color: #f8f9fa;
}

/* Minimap */
.minimap-container {
    position: absolute;
    bottom: 10px;
    left: 10px;
    background-color: rgba(255, 255, 255, 0.9);
    border: 1px solid #ddd;
    border-radius: 4px;
    width: 180px;
    height: 180px;
    overflow: hidden;
    z-index: 100;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
}

.minimap-container::before {
    content: 'Overview Map';
    position: absolute;
    top: 2px;
    left: 5px;
    font-size: 10px;
    color: #666;
    z-index: 101;
}

.minimap-toggle {
    position: absolute;
    top: 0;
    right: 0;
    width: 20px;
    height: 20px;
    font-size: 12px;
    background: #f0f0f0;
    border: 1px solid #ccc;
    cursor: pointer;
    z-index: 101;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
    transition: background-color 0.2s ease;
}

.minimap-toggle:hover {
    background: #e0e0e0;
    color: #0076CE;
}

.minimap-container svg {
    border-radius: 3px;
    margin-top: 18px; /* Make room for the title */
}

.viewport-indicator {
    stroke: #0076CE;
    stroke-width: 2px;
    fill: rgba(0, 118, 206, 0.1);
    pointer-events: none;
    filter: drop-shadow(0 0 2px rgba(0, 118, 206, 0.5));
    transition: all 0.2s ease;
}

.minimap-node {
    transition: all 0.2s ease;
}

.minimap-link {
    stroke-opacity: 0.6;
    transition: all 0.2s ease;
}

/* Responsive styles for different screen sizes */
@media (max-width: 768px) {
    .navigation-controls {
        max-width: 180px;
        padding: 8px;
        font-size: 12px;
    }
    
    .nav-button {
        min-width: 26px;
        min-height: 26px;
        font-size: 12px;
    }
    
    .minimap-container {
        width: 120px;
        height: 120px;
    }
}

/* Legend */
.legend-container {
    position: absolute;
    top: 10px;
    left: 10px;
    background-color: rgba(255, 255, 255, 0.9);
/* Floating contextual navigation panel inside the viz */
.viz-floating-toolbar {
    position: absolute;
    top: 50%;
    right: 5px;
    transform: translateY(-50%);
    background: rgba(255,255,255,0.9);
    border: 1px solid #ccc;
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    gap: 4px;
    padding: 5px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
    z-index: 999;
}

.viz-floating-toolbar button {
    width: 32px;
    height: 32px;
    font-size: 16px;
    font-weight: bold;
    color: #333;
    background-color: #f8f8f8;
    border: 1px solid #bbb;
    border-radius: 6px;
    cursor: pointer;
    transition: 0.2s ease transform, 0.2s ease background;
}

.viz-floating-toolbar button:hover {
    background: #0076CE;
    color: white;
    transform: scale(1.1);
}

.viz-floating-toolbar button:active {
    transform: scale(1.0);
    box-shadow: inset 0 2px 4px rgba(0,0,0,0.2);
}

    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 8px;
    z-index: 100;
    font-size: 12px;
    max-width: 200px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.legend-title {
    font-weight: bold;
    margin-bottom: 8px;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 4px;
}

.legend-circle {
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.legend-square {
    width: 12px;
    height: 12px;
}

.legend-diamond {
    width: 12px;
    height: 12px;
    transform: rotate(45deg);
}

.legend-line {
    width: 20px;
    height: 2px;
}

/* Pulse animation for unpatched products */
@keyframes pulse {
    0% { opacity: 0.5; transform: scale(1); }
    50% { opacity: 0.2; transform: scale(1.2); }
    100% { opacity: 0.5; transform: scale(1); }
}

.pulse-circle {
    animation: pulse 2s infinite ease-in-out;
}