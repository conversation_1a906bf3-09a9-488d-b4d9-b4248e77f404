#!/usr/bin/env python3
"""
Test script for enhanced recommendation features including:
- Product-level filtering
- Remediation tracking and analytics
- Top 5 applications analysis
- Product correlation analysis
"""

import requests
import json
import pandas as pd
from typing import Dict, Any

def test_api_endpoint(url: str, params: Dict[str, str] = None) -> Dict[str, Any]:
    """Test an API endpoint and return the response."""
    try:
        response = requests.get(url, params=params)
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        print(f"Error testing {url}: {e}")
        return {}

def test_filter_options():
    """Test the filter options API to ensure product filter is available."""
    print("Testing filter options API...")
    
    data = test_api_endpoint("http://127.0.0.1:5000/api/filters")
    
    if data:
        print(f"✓ Filter options loaded successfully")
        print(f"  - App names: {len(data.get('app_names', []))} available")
        print(f"  - Owner names: {len(data.get('owner_names', []))} available")
        print(f"  - Products: {len(data.get('products', []))} available")
        print(f"  - L-levels: {len(data.get('l_levels', []))} available")
        
        # Show sample products
        products = data.get('products', [])
        if products:
            print(f"  - Sample products: {products[:5]}")
        
        return True
    else:
        print("✗ Failed to load filter options")
        return False

def test_product_filtering():
    """Test product-level filtering functionality."""
    print("\nTesting product filtering...")
    
    # First get available products
    filter_data = test_api_endpoint("http://127.0.0.1:5000/api/filters")
    products = filter_data.get('products', [])
    
    if not products:
        print("✗ No products available for testing")
        return False
    
    # Test with first product
    test_product = products[0]
    print(f"Testing with product: {test_product}")
    
    data = test_api_endpoint("http://127.0.0.1:5000/api/recommendations", 
                           params={"product": test_product})
    
    if data and 'summary' in data:
        summary = data['summary']
        print(f"✓ Product filtering works")
        print(f"  - Total owners: {summary.get('total_owners', 0)}")
        print(f"  - Total apps: {summary.get('total_apps', 0)}")
        print(f"  - Total servers: {summary.get('total_servers', 0)}")
        
        # Check if product appears in most common products
        products_summary = summary.get('most_common_products', {})
        if test_product in products_summary:
            print(f"  - Product {test_product} found in results: {products_summary[test_product]} occurrences")
        
        return True
    else:
        print("✗ Product filtering failed")
        return False

def test_remediation_analytics():
    """Test remediation tracking and analytics."""
    print("\nTesting remediation analytics...")
    
    data = test_api_endpoint("http://127.0.0.1:5000/api/recommendations")
    
    if data and 'summary' in data:
        summary = data['summary']
        remediation_stats = summary.get('remediation_stats', {})
        
        if remediation_stats:
            print("✓ Remediation statistics available")
            print(f"  - Total items: {remediation_stats.get('total_items', 0)}")
            print(f"  - Remediated: {remediation_stats.get('remediated_count', 0)}")
            print(f"  - Pending: {remediation_stats.get('pending_count', 0)}")
            print(f"  - Success rate: {remediation_stats.get('success_rate', 0)}%")
            print(f"  - Failed: {remediation_stats.get('failed_count', 0)}")
            print(f"  - Unknown: {remediation_stats.get('unknown_count', 0)}")
            return True
        else:
            print("✗ Remediation statistics not found")
            return False
    else:
        print("✗ Failed to get remediation analytics")
        return False

def test_top_applications():
    """Test top 5 applications analysis."""
    print("\nTesting top 5 applications analysis...")
    
    data = test_api_endpoint("http://127.0.0.1:5000/api/recommendations")
    
    if data and 'summary' in data:
        summary = data['summary']
        top_apps = summary.get('top_5_applications', [])
        
        if top_apps:
            print(f"✓ Top applications analysis available ({len(top_apps)} apps)")
            for i, app in enumerate(top_apps[:3], 1):  # Show top 3
                print(f"  {i}. {app.get('name', 'Unknown')}")
                print(f"     - Owner: {app.get('owner', 'Unknown')}")
                print(f"     - Total items: {app.get('total_items', 0)}")
                print(f"     - Remediation rate: {app.get('remediation_rate', 0)}%")
                print(f"     - Success rate: {app.get('success_rate', 0)}%")
                print(f"     - Priority score: {app.get('avg_priority_score', 0)}")
            return True
        else:
            print("✗ Top applications analysis not found")
            return False
    else:
        print("✗ Failed to get top applications analysis")
        return False

def test_product_correlations():
    """Test product correlation analysis."""
    print("\nTesting product correlation analysis...")
    
    data = test_api_endpoint("http://127.0.0.1:5000/api/recommendations")
    
    if data and 'metadata' in data:
        metadata = data['metadata']
        correlations = metadata.get('product_correlations', {})
        
        if correlations:
            print("✓ Product correlation analysis available")
            
            # Show correlation data
            correlation_data = correlations.get('correlations', [])
            insights = correlations.get('insights', [])
            
            print(f"  - Product correlations: {len(correlation_data)} products analyzed")
            if correlation_data:
                top_product = correlation_data[0]
                print(f"  - Best performing product: {top_product.get('product', 'Unknown')} "
                      f"({top_product.get('success_rate', 0)}% success rate)")
            
            print(f"  - Insights generated: {len(insights)}")
            for insight in insights[:2]:  # Show first 2 insights
                print(f"    • {insight}")
            
            return True
        else:
            print("✗ Product correlation analysis not found")
            return False
    else:
        print("✗ Failed to get product correlation analysis")
        return False

def test_combined_filtering():
    """Test combined filtering with multiple parameters."""
    print("\nTesting combined filtering...")
    
    # Get filter options first
    filter_data = test_api_endpoint("http://127.0.0.1:5000/api/filters")
    products = filter_data.get('products', [])
    owners = filter_data.get('owner_names', [])
    
    if not products or not owners:
        print("✗ Insufficient filter options for combined testing")
        return False
    
    # Test with product + owner filter
    test_product = products[0]
    test_owner = owners[0]
    
    print(f"Testing with product: {test_product} and owner: {test_owner}")
    
    data = test_api_endpoint("http://127.0.0.1:5000/api/recommendations", 
                           params={"product": test_product, "app_owner": test_owner})
    
    if data and 'summary' in data:
        summary = data['summary']
        print("✓ Combined filtering works")
        print(f"  - Results: {summary.get('total_owners', 0)} owners, "
              f"{summary.get('total_apps', 0)} apps, {summary.get('total_servers', 0)} servers")
        return True
    else:
        print("✗ Combined filtering failed")
        return False

def main():
    """Run all tests."""
    print("=== Enhanced Recommendation Features Test ===\n")
    
    tests = [
        test_filter_options,
        test_product_filtering,
        test_remediation_analytics,
        test_top_applications,
        test_product_correlations,
        test_combined_filtering
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"✗ Test {test.__name__} failed with error: {e}")
            results.append(False)
    
    # Summary
    passed = sum(results)
    total = len(results)
    
    print(f"\n=== Test Summary ===")
    print(f"Passed: {passed}/{total}")
    print(f"Success rate: {passed/total*100:.1f}%")
    
    if passed == total:
        print("🎉 All tests passed! Enhanced features are working correctly.")
    else:
        print("⚠️  Some tests failed. Please check the implementation.")

if __name__ == "__main__":
    main()
