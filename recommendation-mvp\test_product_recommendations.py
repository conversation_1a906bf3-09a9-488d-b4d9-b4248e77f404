#!/usr/bin/env python3
"""
Comprehensive test script for product-level recommendations including:
- Product-to-Product correlations
- Owner-Product affinity analysis
- Cross-Product recommendations
- Correlation matrix validation
- Statistical significance testing
"""

import requests
import json
import pandas as pd
from typing import Dict, Any
import time

def test_api_endpoint(url: str, params: Dict[str, str] = None) -> Dict[str, Any]:
    """Test an API endpoint and return the response."""
    try:
        response = requests.get(url, params=params)
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        print(f"Error testing {url}: {e}")
        return {}

def test_product_level_recommendations_api():
    """Test the new product-level recommendations API endpoint."""
    print("Testing Product-Level Recommendations API...")
    
    # Test basic endpoint
    data = test_api_endpoint("http://127.0.0.1:5000/api/product-recommendations")
    
    if data:
        print("✓ Product-level recommendations API accessible")
        
        # Check structure
        expected_keys = ["product_to_product", "owner_product_affinities", "cross_product_recommendations", "correlation_matrix", "insights"]
        missing_keys = [key for key in expected_keys if key not in data]
        
        if not missing_keys:
            print("✓ All expected data structures present")
        else:
            print(f"✗ Missing keys: {missing_keys}")
            return False
        
        # Check data content
        print(f"  - Product-to-Product recommendations: {len(data.get('product_to_product', []))}")
        print(f"  - Owner-Product affinities: {len(data.get('owner_product_affinities', []))}")
        print(f"  - Cross-Product recommendations: {len(data.get('cross_product_recommendations', []))}")
        print(f"  - Correlation matrix size: {len(data.get('correlation_matrix', {}))}")
        print(f"  - Insights generated: {len(data.get('insights', []))}")
        
        return True
    else:
        print("✗ Failed to access product-level recommendations API")
        return False

def test_product_to_product_recommendations():
    """Test product-to-product recommendation functionality."""
    print("\nTesting Product-to-Product Recommendations...")
    
    data = test_api_endpoint("http://127.0.0.1:5000/api/product-recommendations", 
                           params={"type": "product_to_product"})
    
    if data and "product_to_product" in data:
        recommendations = data["product_to_product"]
        print(f"✓ Generated {len(recommendations)} product-to-product recommendations")
        
        if recommendations:
            # Analyze first recommendation
            rec = recommendations[0]
            required_fields = ["target_product", "recommended_product", "correlation_score", 
                             "confidence_level", "reasoning", "statistical_significance"]
            
            missing_fields = [field for field in required_fields if field not in rec]
            if not missing_fields:
                print("✓ Recommendation structure is complete")
                print(f"  - Top recommendation: {rec['target_product']} → {rec['recommended_product']}")
                print(f"  - Correlation score: {rec['correlation_score']:.3f}")
                print(f"  - Confidence level: {rec['confidence_level']:.3f}")
                print(f"  - Reasoning: {rec['reasoning'][:100]}...")
                
                # Check correlation matrix
                if "correlation_matrix" in data:
                    matrix = data["correlation_matrix"]
                    print(f"✓ Correlation matrix includes {len(matrix)} products")
                    
                    # Validate matrix symmetry and self-correlation
                    if matrix:
                        first_product = list(matrix.keys())[0]
                        if first_product in matrix and matrix[first_product].get(first_product) == 1.0:
                            print("✓ Correlation matrix shows correct self-correlation")
                        else:
                            print("✗ Correlation matrix self-correlation issue")
                
                return True
            else:
                print(f"✗ Missing recommendation fields: {missing_fields}")
                return False
        else:
            print("⚠️  No product-to-product recommendations generated")
            return True  # Not necessarily an error if data is limited
    else:
        print("✗ Failed to get product-to-product recommendations")
        return False

def test_owner_product_affinities():
    """Test owner-product affinity analysis."""
    print("\nTesting Owner-Product Affinity Analysis...")
    
    data = test_api_endpoint("http://127.0.0.1:5000/api/product-recommendations", 
                           params={"type": "owner_affinity"})
    
    if data and "owner_product_affinities" in data:
        affinities = data["owner_product_affinities"]
        print(f"✓ Generated {len(affinities)} owner-product affinities")
        
        if affinities:
            # Analyze expertise levels
            expertise_levels = {}
            for affinity in affinities:
                level = affinity.get("expertise_level", "Unknown")
                expertise_levels[level] = expertise_levels.get(level, 0) + 1
            
            print("✓ Expertise level distribution:")
            for level, count in expertise_levels.items():
                print(f"    {level}: {count}")
            
            # Check top affinity
            top_affinity = affinities[0]
            print(f"✓ Top affinity: {top_affinity['owner']} with {top_affinity['product']}")
            print(f"  - Affinity score: {top_affinity['affinity_score']:.3f}")
            print(f"  - Success rate: {top_affinity['success_rate']:.3f}")
            print(f"  - Execution count: {top_affinity['execution_count']}")
            print(f"  - Expertise level: {top_affinity['expertise_level']}")
            
            # Check if recommendations are provided for owners
            if "recommended_products" in top_affinity and top_affinity["recommended_products"]:
                print(f"✓ Owner has {len(top_affinity['recommended_products'])} product recommendations")
            
            return True
        else:
            print("⚠️  No owner-product affinities generated")
            return True
    else:
        print("✗ Failed to get owner-product affinities")
        return False

def test_cross_product_recommendations():
    """Test cross-product recommendation functionality."""
    print("\nTesting Cross-Product Recommendations...")
    
    data = test_api_endpoint("http://127.0.0.1:5000/api/product-recommendations", 
                           params={"type": "cross_product"})
    
    if data and "cross_product_recommendations" in data:
        cross_recs = data["cross_product_recommendations"]
        print(f"✓ Generated {len(cross_recs)} cross-product recommendations")
        
        if cross_recs:
            # Analyze first recommendation
            rec = cross_recs[0]
            required_fields = ["primary_product", "secondary_product", "correlation_coefficient",
                             "confidence_score", "environment_pattern", "cluster_pattern"]
            
            missing_fields = [field for field in required_fields if field not in rec]
            if not missing_fields:
                print("✓ Cross-product recommendation structure is complete")
                print(f"  - Primary → Secondary: {rec['primary_product']} → {rec['secondary_product']}")
                print(f"  - Correlation coefficient: {rec['correlation_coefficient']:.3f}")
                print(f"  - Confidence score: {rec['confidence_score']:.3f}")
                print(f"  - Environment pattern: {rec['environment_pattern']}")
                print(f"  - Combined success rate: {rec.get('success_rate_combined', 'N/A')}")
                
                return True
            else:
                print(f"✗ Missing cross-product fields: {missing_fields}")
                return False
        else:
            print("⚠️  No cross-product recommendations generated")
            return True
    else:
        print("✗ Failed to get cross-product recommendations")
        return False

def test_filtering_capabilities():
    """Test filtering capabilities of product recommendations."""
    print("\nTesting Filtering Capabilities...")
    
    # Test confidence filtering
    print("Testing confidence filtering...")
    high_confidence_data = test_api_endpoint("http://127.0.0.1:5000/api/product-recommendations", 
                                           params={"confidence": "0.5"})
    
    if high_confidence_data:
        p2p_count = len(high_confidence_data.get("product_to_product", []))
        cross_count = len(high_confidence_data.get("cross_product_recommendations", []))
        print(f"✓ High confidence filter (≥0.5): {p2p_count} P2P, {cross_count} cross-product")
    
    # Test product filtering
    print("Testing product filtering...")
    filter_data = test_api_endpoint("http://127.0.0.1:5000/api/filters")
    if filter_data and "products" in filter_data:
        products = filter_data["products"]
        if products:
            test_product = products[0]
            product_filtered_data = test_api_endpoint("http://127.0.0.1:5000/api/product-recommendations", 
                                                    params={"product": test_product})
            
            if product_filtered_data:
                print(f"✓ Product filtering works for: {test_product}")
                return True
    
    print("⚠️  Limited filtering test due to data constraints")
    return True

def test_statistical_significance():
    """Test statistical significance calculations."""
    print("\nTesting Statistical Significance...")
    
    data = test_api_endpoint("http://127.0.0.1:5000/api/product-recommendations")
    
    if data and "product_to_product" in data:
        recommendations = data["product_to_product"]
        
        if recommendations:
            # Check statistical significance values
            sig_values = [rec.get("statistical_significance", 0) for rec in recommendations]
            valid_sig_values = [val for val in sig_values if 0 <= val <= 1]
            
            if len(valid_sig_values) == len(sig_values):
                print("✓ All statistical significance values are in valid range [0,1]")
                print(f"  - Average significance: {sum(valid_sig_values)/len(valid_sig_values):.3f}")
                print(f"  - Max significance: {max(valid_sig_values):.3f}")
                print(f"  - Min significance: {min(valid_sig_values):.3f}")
                return True
            else:
                print(f"✗ Invalid statistical significance values found")
                return False
        else:
            print("⚠️  No recommendations to test statistical significance")
            return True
    else:
        print("✗ Failed to get data for statistical significance testing")
        return False

def test_integration_with_main_api():
    """Test integration with main recommendations API."""
    print("\nTesting Integration with Main API...")
    
    main_data = test_api_endpoint("http://127.0.0.1:5000/api/recommendations")
    
    if main_data and "metadata" in main_data:
        metadata = main_data["metadata"]
        
        if "product_level_recommendations" in metadata:
            product_recs = metadata["product_level_recommendations"]
            print("✓ Product-level recommendations integrated in main API")
            
            # Check structure
            expected_keys = ["product_to_product", "owner_product_affinities", "cross_product_recommendations"]
            present_keys = [key for key in expected_keys if key in product_recs]
            
            print(f"  - Integrated components: {', '.join(present_keys)}")
            
            if len(present_keys) == len(expected_keys):
                print("✓ All product recommendation components integrated")
                return True
            else:
                print(f"⚠️  Some components missing: {set(expected_keys) - set(present_keys)}")
                return True
        else:
            print("✗ Product-level recommendations not found in main API")
            return False
    else:
        print("✗ Failed to get main API data")
        return False

def main():
    """Run all product recommendation tests."""
    print("=== Product-Level Recommendations Test Suite ===\n")
    
    tests = [
        test_product_level_recommendations_api,
        test_product_to_product_recommendations,
        test_owner_product_affinities,
        test_cross_product_recommendations,
        test_filtering_capabilities,
        test_statistical_significance,
        test_integration_with_main_api
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
            time.sleep(0.5)  # Brief pause between tests
        except Exception as e:
            print(f"✗ Test {test.__name__} failed with error: {e}")
            results.append(False)
    
    # Summary
    passed = sum(results)
    total = len(results)
    
    print(f"\n=== Test Summary ===")
    print(f"Passed: {passed}/{total}")
    print(f"Success rate: {passed/total*100:.1f}%")
    
    if passed == total:
        print("🎉 All product recommendation tests passed!")
        print("✅ Product-level recommendations are working correctly with:")
        print("   • Python-based correlation analysis")
        print("   • Statistical significance calculations")
        print("   • Owner-product affinity tracking")
        print("   • Cross-product pattern recognition")
        print("   • Confidence-based filtering")
    else:
        print("⚠️  Some tests failed. Please check the implementation.")

if __name__ == "__main__":
    main()
