<div class="recommendation-funnel-section">
    <h2>Organizational Recommendation Funnel</h2>
    <div class="funnel-container">
        <div class="funnel-level l4">
            <div class="funnel-header">
                <div class="funnel-title">
                    <span class="level-badge">L4</span>
                    <span class="level-title">Management Level Placeholder</span>
                </div>
                <div class="funnel-personnel">
                     <div class="personnel-avatar">RM</div>
                     <div class="personnel-name"><PERSON></div>
                 </div>
            </div>

            <div class="funnel-content">
                <div class="funnel-metrics">
                    <div class="metric-card">
                        <div class="metric-label">Most Successful Product:</div>
                        <div class="metric-value">TLS/SSL Server Supports Static Key Ciphers</div>
                        <div class="metric-detail">(333 executions)</div>
                        <div class="success-rate-bar"><div class="success-rate-progress success-rate-high" style="width: 49%;"></div></div>
                        <div class="success-rate-text">49%</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-label">Recommended Operation:</div>
                         <div class="metric-value">Remediate</div>
                        <div class="metric-detail">(49% success rate)</div>
                        <div class="success-rate-bar"><div class="success-rate-progress success-rate-medium" style="width: 49%;"></div></div>
                         <div class="success-rate-text">49%</div>
                    </div>
                </div>
                <div class="funnel-description">Management level recommendations focus on strategic product adoption across teams.</div>
                <div class="app-context">
                    <div class="app-context-title">Applications Affected:</div>
                    <div class="app-tags">
                        <div class="app-tag"><span>HR Portal</span></div>
                        <div class="app-tag"><span>Payment System</span></div>
                        <div class="app-tag"><span>CRM</span></div>
                    </div>
                </div>
            </div>
        </div>

        <div class="funnel-arrow" data-context="Recommendations flow from strategic to tactical">
            <div class="arrow-line"></div>
            <div class="arrow-head"></div>
            <div class="flow-context">
                <div class="context-icon">i</div>
                <div class="context-text">Recommendations flow from strategic to tactical</div>
            </div>
        </div>

        <div class="funnel-level l5">
             <div class="funnel-header">
                 <div class="funnel-title">
                     <span class="level-badge">L5</span>
                     <span class="level-title">Team (Mike Kavanagh)</span>
                 </div>
                  <div class="funnel-personnel">
                      <div class="personnel-avatar">MK</div>
                      <div class="personnel-name">Mike Kavanagh</div>
                  </div>
             </div>
             <div class="funnel-content">
                 <div class="funnel-metrics">
                     <div class="metric-card">
                         <div class="metric-label">Most Successful Product:</div>
                         <div class="metric-value">TLS/SSL Server Supports Static Key Ciphers</div>
                         <div class="metric-detail">(333 executions)</div>
                         <div class="success-rate-bar"><div class="success-rate-progress success-rate-high" style="width: 49%;"></div></div>
                         <div class="success-rate-text">49%</div>
                     </div>
                     <div class="metric-card">
                         <div class="metric-label">Recommended Operation:</div>
                         <div class="metric-value">Remediate</div>
                         <div class="metric-detail">(49% success rate)</div>
                         <div class="success-rate-bar"><div class="success-rate-progress success-rate-medium" style="width: 49%;"></div></div>
                         <div class="success-rate-text">49%</div>
                     </div>
                 </div>
                 <div class="funnel-description">Team level recommendations focus on tactical implementation and standardization.</div>
                 <div class="app-context">
                     <div class="app-context-title">Applications Affected:</div>
                     <div class="app-tags">
                         <div class="app-tag"><span>HR Portal</span></div>
                         <div class="app-tag"><span>Payment API</span></div>
                     </div>
                 </div>
             </div>
        </div>

        <div class="funnel-arrow" data-context="Recommendations flow from tactical to implementation">
             <div class="arrow-line"></div>
             <div class="arrow-head"></div>
             <div class="flow-context">
                 <div class="context-icon">i</div>
                 <div class="context-text">Recommendations flow from tactical to implementation</div>
             </div>
         </div>

        <div class="funnel-level l6l7">
             <div class="funnel-header">
                 <div class="funnel-title">
                     <span class="level-badge">L6/L7</span>
                     <span class="level-title">Implementation</span>
                 </div>
                  <div class="funnel-personnel">
                      <div class="personnel-avatar">DT</div>
                      <div class="personnel-name">DevOps Team</div>
                  </div>
             </div>
             <div class="funnel-content">
                 <div class="funnel-metrics">
                     <div class="metric-card">
                         <div class="metric-label">Most Successful Product:</div>
                         <div class="metric-value">TLS/SSL Server Supports Static Key Ciphers</div>
                         <div class="metric-detail">(333 executions)</div>
                         <div class="success-rate-bar"><div class="success-rate-progress success-rate-high" style="width: 53%;"></div></div>
                         <div class="success-rate-text">53%</div>
                     </div>
                     <div class="metric-card">
                         <div class="metric-label">Recommended Operation:</div>
                         <div class="metric-value">Remediate</div>
                         <div class="metric-detail">(53% success rate)</div>
                         <div class="success-rate-bar"><div class="success-rate-progress success-rate-medium" style="width: 53%;"></div></div>
                         <div class="success-rate-text">53%</div>
                     </div>
                 </div>
                 <div class="funnel-description">Implementation level focuses on technical execution details for specific applications.</div>
                 <div class="app-context">
                     <div class="app-context-title">Applications Affected:</div>
                     <div class="app-tags">
                         <div class="app-tag"><span>HR Portal</span></div>
                         <div class="app-tag"><span>Payment API</span></div>
                     </div>
                 </div>
                 <div class="implementation-tasks">
                     <div class="tasks-title">Implementation Tasks:</div>
                     <ul class="task-list">
                         <li class="task-item">
                             <div class="task-name">Update OpenSSL configuration</div>
                             <div class="task-status task-status-in-progress">In Progress</div>
                         </li>
                         <li class="task-item">
                             <div class="task-name">Disable static key ciphers</div>
                             <div class="task-status task-status-pending">Pending</div>
                         </li>
                         <li class="task-item">
                             <div class="task-name">Test TLS connections</div>
                             <div class="task-status task-status-pending">Pending</div>
                         </li>
                     </ul>
                 </div>
             </div>
         </div>

    </div> </div>
