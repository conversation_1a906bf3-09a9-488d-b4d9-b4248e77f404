#!/usr/bin/env python3
"""
Performance optimization test script for product-level recommendations.
Tests the caching system, pre-computation, and response times.
"""

import requests
import time
import json
import statistics
from typing import Dict, List, Any
import concurrent.futures
import threading

class PerformanceTestSuite:
    def __init__(self, base_url: str = "http://127.0.0.1:5000"):
        self.base_url = base_url
        self.test_results = {}
        
    def measure_response_time(self, url: str, params: Dict[str, str] = None, iterations: int = 3) -> Dict[str, Any]:
        """Measure response time for multiple iterations."""
        times = []
        responses = []
        
        for i in range(iterations):
            start_time = time.time()
            try:
                response = requests.get(url, params=params, timeout=30)
                end_time = time.time()
                
                if response.status_code == 200:
                    times.append(end_time - start_time)
                    responses.append(response.json())
                else:
                    print(f"Error: HTTP {response.status_code} for {url}")
                    return {"error": f"HTTP {response.status_code}"}
                    
            except requests.exceptions.Timeout:
                print(f"Timeout for {url}")
                return {"error": "Timeout"}
            except Exception as e:
                print(f"Error for {url}: {e}")
                return {"error": str(e)}
        
        if times:
            return {
                "avg_time": statistics.mean(times),
                "min_time": min(times),
                "max_time": max(times),
                "median_time": statistics.median(times),
                "times": times,
                "response_size": len(json.dumps(responses[0])) if responses else 0,
                "success": True
            }
        else:
            return {"error": "No successful responses", "success": False}

    def test_cache_performance(self):
        """Test caching performance - first call vs subsequent calls."""
        print("Testing Cache Performance...")
        
        url = f"{self.base_url}/api/product-recommendations"
        
        # First call (cache miss)
        print("  First call (cache miss)...")
        first_call = self.measure_response_time(url, iterations=1)
        
        if not first_call.get("success", False):
            print(f"  ✗ First call failed: {first_call.get('error')}")
            return False
        
        # Wait a moment for cache to be populated
        time.sleep(0.5)
        
        # Subsequent calls (cache hits)
        print("  Subsequent calls (cache hits)...")
        cached_calls = self.measure_response_time(url, iterations=5)
        
        if not cached_calls.get("success", False):
            print(f"  ✗ Cached calls failed: {cached_calls.get('error')}")
            return False
        
        # Compare performance
        first_time = first_call["times"][0]
        avg_cached_time = cached_calls["avg_time"]
        speedup = first_time / avg_cached_time if avg_cached_time > 0 else 0
        
        print(f"  ✓ First call time: {first_time:.2f}s")
        print(f"  ✓ Average cached call time: {avg_cached_time:.2f}s")
        print(f"  ✓ Cache speedup: {speedup:.1f}x")
        
        # Performance criteria
        if first_time <= 10.0:  # First call should be under 10 seconds
            print(f"  ✓ First call performance acceptable ({first_time:.2f}s ≤ 10s)")
        else:
            print(f"  ⚠️  First call slower than expected ({first_time:.2f}s > 10s)")
        
        if avg_cached_time <= 2.0:  # Cached calls should be under 2 seconds
            print(f"  ✓ Cached call performance excellent ({avg_cached_time:.2f}s ≤ 2s)")
        else:
            print(f"  ⚠️  Cached calls slower than target ({avg_cached_time:.2f}s > 2s)")
        
        self.test_results["cache_performance"] = {
            "first_call_time": first_time,
            "avg_cached_time": avg_cached_time,
            "speedup": speedup,
            "meets_performance_criteria": first_time <= 10.0 and avg_cached_time <= 2.0
        }
        
        return True

    def test_filtered_requests_performance(self):
        """Test performance with different filters."""
        print("\nTesting Filtered Requests Performance...")
        
        base_url = f"{self.base_url}/api/product-recommendations"
        
        test_cases = [
            {"name": "No filters", "params": {}},
            {"name": "Product filter", "params": {"product": "WSUS"}},
            {"name": "Type filter", "params": {"type": "product_to_product"}},
            {"name": "Confidence filter", "params": {"confidence": "0.5"}},
            {"name": "Combined filters", "params": {"type": "owner_affinity", "confidence": "0.3"}}
        ]
        
        results = {}
        
        for test_case in test_cases:
            print(f"  Testing: {test_case['name']}")
            result = self.measure_response_time(base_url, params=test_case["params"])
            
            if result.get("success", False):
                print(f"    ✓ Average time: {result['avg_time']:.2f}s")
                print(f"    ✓ Response size: {result['response_size']:,} bytes")
                results[test_case["name"]] = result
            else:
                print(f"    ✗ Failed: {result.get('error')}")
                results[test_case["name"]] = result
        
        # Check if all filtered requests are fast
        all_fast = all(
            r.get("avg_time", float('inf')) <= 3.0 
            for r in results.values() 
            if r.get("success", False)
        )
        
        if all_fast:
            print("  ✓ All filtered requests meet performance criteria (≤ 3s)")
        else:
            print("  ⚠️  Some filtered requests exceed performance target")
        
        self.test_results["filtered_performance"] = results
        return all_fast

    def test_concurrent_requests(self):
        """Test performance under concurrent load."""
        print("\nTesting Concurrent Request Performance...")
        
        url = f"{self.base_url}/api/product-recommendations"
        num_concurrent = 5
        
        def make_request():
            start_time = time.time()
            try:
                response = requests.get(url, timeout=30)
                end_time = time.time()
                return {
                    "time": end_time - start_time,
                    "status": response.status_code,
                    "success": response.status_code == 200
                }
            except Exception as e:
                return {"error": str(e), "success": False}
        
        print(f"  Making {num_concurrent} concurrent requests...")
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=num_concurrent) as executor:
            futures = [executor.submit(make_request) for _ in range(num_concurrent)]
            results = [future.result() for future in concurrent.futures.as_completed(futures)]
        
        successful_results = [r for r in results if r.get("success", False)]
        
        if successful_results:
            times = [r["time"] for r in successful_results]
            avg_time = statistics.mean(times)
            max_time = max(times)
            
            print(f"  ✓ Successful requests: {len(successful_results)}/{num_concurrent}")
            print(f"  ✓ Average response time: {avg_time:.2f}s")
            print(f"  ✓ Maximum response time: {max_time:.2f}s")
            
            # Performance criteria for concurrent requests
            performance_ok = avg_time <= 5.0 and max_time <= 10.0
            
            if performance_ok:
                print("  ✓ Concurrent performance meets criteria")
            else:
                print("  ⚠️  Concurrent performance below expectations")
            
            self.test_results["concurrent_performance"] = {
                "successful_requests": len(successful_results),
                "total_requests": num_concurrent,
                "avg_time": avg_time,
                "max_time": max_time,
                "meets_criteria": performance_ok
            }
            
            return performance_ok
        else:
            print("  ✗ No successful concurrent requests")
            return False

    def test_data_structure_efficiency(self):
        """Test the efficiency of returned data structures."""
        print("\nTesting Data Structure Efficiency...")
        
        url = f"{self.base_url}/api/product-recommendations"
        
        result = self.measure_response_time(url, iterations=1)
        
        if not result.get("success", False):
            print("  ✗ Failed to get response for data structure test")
            return False
        
        # Get the actual response
        response = requests.get(url)
        data = response.json()
        
        # Analyze data structure
        structure_analysis = {
            "total_size_bytes": len(json.dumps(data)),
            "product_to_product_count": len(data.get("product_to_product", [])),
            "owner_affinities_count": len(data.get("owner_product_affinities", [])),
            "cross_product_count": len(data.get("cross_product_recommendations", [])),
            "correlation_matrix_size": len(data.get("correlation_matrix", {})),
            "insights_count": len(data.get("insights", []))
        }
        
        print(f"  ✓ Total response size: {structure_analysis['total_size_bytes']:,} bytes")
        print(f"  ✓ Product-to-product recommendations: {structure_analysis['product_to_product_count']}")
        print(f"  ✓ Owner-product affinities: {structure_analysis['owner_affinities_count']}")
        print(f"  ✓ Cross-product recommendations: {structure_analysis['cross_product_count']}")
        print(f"  ✓ Correlation matrix products: {structure_analysis['correlation_matrix_size']}")
        print(f"  ✓ Generated insights: {structure_analysis['insights_count']}")
        
        # Check if data is reasonable size (not too large, not empty)
        size_ok = 1000 <= structure_analysis['total_size_bytes'] <= 1000000  # 1KB to 1MB
        content_ok = (
            structure_analysis['product_to_product_count'] > 0 or
            structure_analysis['owner_affinities_count'] > 0 or
            structure_analysis['cross_product_count'] > 0
        )
        
        if size_ok and content_ok:
            print("  ✓ Data structure efficiency is good")
        else:
            print("  ⚠️  Data structure may need optimization")
        
        self.test_results["data_structure"] = structure_analysis
        return size_ok and content_ok

    def test_memory_usage_stability(self):
        """Test memory usage stability over multiple requests."""
        print("\nTesting Memory Usage Stability...")
        
        url = f"{self.base_url}/api/product-recommendations"
        
        # Make multiple requests to test memory stability
        print("  Making 10 sequential requests to test memory stability...")
        
        times = []
        for i in range(10):
            result = self.measure_response_time(url, iterations=1)
            if result.get("success", False):
                times.append(result["times"][0])
            else:
                print(f"  ✗ Request {i+1} failed")
                return False
        
        # Check if response times are stable (not increasing significantly)
        if len(times) >= 5:
            first_half_avg = statistics.mean(times[:5])
            second_half_avg = statistics.mean(times[5:])
            
            print(f"  ✓ First 5 requests average: {first_half_avg:.2f}s")
            print(f"  ✓ Last 5 requests average: {second_half_avg:.2f}s")
            
            # Memory leak indicator: significant increase in response time
            memory_stable = second_half_avg <= first_half_avg * 1.5
            
            if memory_stable:
                print("  ✓ Memory usage appears stable")
            else:
                print("  ⚠️  Possible memory leak detected")
            
            self.test_results["memory_stability"] = {
                "first_half_avg": first_half_avg,
                "second_half_avg": second_half_avg,
                "stable": memory_stable
            }
            
            return memory_stable
        
        return False

    def run_all_tests(self):
        """Run all performance tests."""
        print("=== Product Recommendation Performance Test Suite ===\n")
        
        tests = [
            ("Cache Performance", self.test_cache_performance),
            ("Filtered Requests", self.test_filtered_requests_performance),
            ("Concurrent Requests", self.test_concurrent_requests),
            ("Data Structure Efficiency", self.test_data_structure_efficiency),
            ("Memory Stability", self.test_memory_usage_stability)
        ]
        
        results = []
        
        for test_name, test_func in tests:
            try:
                result = test_func()
                results.append(result)
            except Exception as e:
                print(f"✗ {test_name} failed with error: {e}")
                results.append(False)
        
        # Summary
        passed = sum(results)
        total = len(results)
        
        print(f"\n=== Performance Test Summary ===")
        print(f"Tests passed: {passed}/{total}")
        print(f"Success rate: {passed/total*100:.1f}%")
        
        if passed == total:
            print("🎉 All performance tests passed!")
            print("✅ Product recommendation optimization is working correctly:")
            print("   • Caching system provides significant speedup")
            print("   • Response times meet performance criteria")
            print("   • System handles concurrent requests well")
            print("   • Memory usage is stable")
        else:
            print("⚠️  Some performance tests failed. Review the optimization.")
        
        return passed == total

if __name__ == "__main__":
    test_suite = PerformanceTestSuite()
    test_suite.run_all_tests()
